import{m as c,a as n}from"./vendor-0d926b1a.js";window.Alpine=c;window.axios=n;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const a=document.head.querySelector('meta[name="csrf-token"]');a&&(window.axios.defaults.headers.common["X-CSRF-TOKEN"]=a.content);class l{constructor(){this.currentTheme="modern",this.isDarkMode=this.getStoredDarkMode(),this.init()}init(){this.applyTheme(),this.setupEventListeners(),this.initializeComponents()}getStoredDarkMode(){const e=localStorage.getItem("darkMode");return e!==null?JSON.parse(e):window.matchMedia("(prefers-color-scheme: dark)").matches}applyTheme(){this.isDarkMode?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}toggleDarkMode(){this.isDarkMode=!this.isDarkMode,localStorage.setItem("darkMode",JSON.stringify(this.isDarkMode)),this.applyTheme(),this.updateServerDarkMode(),window.dispatchEvent(new CustomEvent("darkModeToggled",{detail:{isDarkMode:this.isDarkMode}}))}async updateServerDarkMode(){try{await n.post("/api/theme/toggle-dark-mode")}catch(e){console.error("Failed to update dark mode on server:",e)}}setupEventListeners(){window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{localStorage.getItem("darkMode")===null&&(this.isDarkMode=e.matches,this.applyTheme())})}initializeComponents(){this.initLazyLoading(),this.initSmoothScrolling(),this.initScrollAnimations(),this.initPerformanceOptimizations()}initLazyLoading(){if("IntersectionObserver"in window){const e=new IntersectionObserver((t,s)=>{t.forEach(r=>{if(r.isIntersecting){const i=r.target;i.src=i.dataset.src,i.classList.remove("lazy"),i.classList.add("fade-in"),s.unobserve(i)}})});document.querySelectorAll("img[data-src]").forEach(t=>{e.observe(t)})}}initSmoothScrolling(){document.querySelectorAll('a[href^="#"]').forEach(e=>{e.addEventListener("click",function(t){var r;t.preventDefault();const s=document.querySelector(this.getAttribute("href"));if(s){const i=((r=document.querySelector("header"))==null?void 0:r.offsetHeight)||0,d=s.offsetTop-i-20;window.scrollTo({top:d,behavior:"smooth"})}})})}initScrollAnimations(){if("IntersectionObserver"in window){const e=new IntersectionObserver(t=>{t.forEach(s=>{s.isIntersecting&&s.target.classList.add("animate-in")})},{threshold:.1,rootMargin:"0px 0px -50px 0px"});document.querySelectorAll("[data-animate]").forEach(t=>{e.observe(t)})}}initPerformanceOptimizations(){this.preloadCriticalResources(),this.optimizeImages(),this.setupServiceWorker()}preloadCriticalResources(){["/themes/modern/css/app.css","/themes/modern/js/app.js"].forEach(t=>{const s=document.createElement("link");s.rel="preload",s.href=t,s.as=t.endsWith(".css")?"style":"script",document.head.appendChild(s)})}optimizeImages(){document.querySelectorAll("img:not([loading])").forEach((t,s)=>{s>2&&(t.loading="lazy")})}async setupServiceWorker(){if("serviceWorker"in navigator)try{await navigator.serviceWorker.register("/sw.js"),console.log("Service Worker registered successfully")}catch(e){console.log("Service Worker registration failed:",e)}}}class m{constructor(){this.activeSection="home",this.sections=[],this.navLinks=[],this.init()}init(){this.sections=document.querySelectorAll("section[id]"),this.navLinks=document.querySelectorAll(".nav-link"),this.setupIntersectionObserver()}setupIntersectionObserver(){const e=new IntersectionObserver(t=>{t.forEach(s=>{s.isIntersecting&&this.setActiveSection(s.target.id)})},{threshold:.3,rootMargin:"-80px 0px -80px 0px"});this.sections.forEach(t=>{e.observe(t)})}setActiveSection(e){this.activeSection=e,this.navLinks.forEach(s=>{s.classList.remove("text-primary-600","dark:text-primary-400","active"),s.classList.add("text-gray-700","dark:text-gray-300")});const t=document.querySelector(`.nav-link[href="#${e}"]`);t&&(t.classList.remove("text-gray-700","dark:text-gray-300"),t.classList.add("text-primary-600","dark:text-primary-400","active"))}}class h{constructor(){this.init()}init(){this.setupContactForm(),this.setupNewsletterForm()}setupContactForm(){const e=document.getElementById("contact-form");e&&e.addEventListener("submit",this.handleContactSubmit.bind(this))}setupNewsletterForm(){const e=document.getElementById("newsletter-form");e&&e.addEventListener("submit",this.handleNewsletterSubmit.bind(this))}async handleContactSubmit(e){e.preventDefault();const t=e.target,s=new FormData(t),r=t.querySelector('button[type="submit"]');try{this.setButtonLoading(r,!0),(await n.post("/api/contact",s)).data.success?(this.showNotification("Message sent successfully!","success"),t.reset()):this.showNotification("Failed to send message. Please try again.","error")}catch(i){this.showNotification("An error occurred. Please try again.","error"),console.error("Contact form error:",i)}finally{this.setButtonLoading(r,!1)}}async handleNewsletterSubmit(e){e.preventDefault();const t=e.target,s=new FormData(t),r=t.querySelector('button[type="submit"]');try{this.setButtonLoading(r,!0),(await n.post("/api/newsletter",s)).data.success?(this.showNotification("Successfully subscribed to newsletter!","success"),t.reset()):this.showNotification("Failed to subscribe. Please try again.","error")}catch(i){this.showNotification("An error occurred. Please try again.","error"),console.error("Newsletter form error:",i)}finally{this.setButtonLoading(r,!1)}}setButtonLoading(e,t){t?(e.disabled=!0,e.innerHTML='<div class="loading-spinner w-5 h-5 mr-2"></div>Sending...'):(e.disabled=!1,e.innerHTML=e.dataset.originalText||"Send Message")}showNotification(e,t="info"){const s=document.createElement("div");s.className=`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${t==="success"?"bg-green-500 text-white":t==="error"?"bg-red-500 text-white":"bg-blue-500 text-white"}`,s.textContent=e,document.body.appendChild(s),setTimeout(()=>{s.classList.remove("translate-x-full")},100),setTimeout(()=>{s.classList.add("translate-x-full"),setTimeout(()=>{document.body.removeChild(s)},300)},5e3)}}document.addEventListener("DOMContentLoaded",()=>{window.themeManager=new l,window.navigationManager=new m,window.formHandler=new h,c.start(),setTimeout(()=>{const o=document.getElementById("loading-screen");o&&(o.style.opacity="0",setTimeout(()=>{o.style.display="none"},500))},1e3)});
