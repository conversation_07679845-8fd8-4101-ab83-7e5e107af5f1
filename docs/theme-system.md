# Modern Theme System Documentation

## Overview

The Modern Theme System is a comprehensive, modular theming solution for Laravel applications. It provides a flexible architecture for creating, managing, and switching between different themes while maintaining performance and accessibility standards.

## Features

- **Modular Architecture**: Each theme is self-contained with its own assets, views, and configuration
- **Dark Mode Support**: Built-in dark/light mode toggle with system preference detection
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels and keyboard navigation
- **Performance Optimized**: Lazy loading, asset optimization, and caching
- **API-Driven**: RESTful API for theme management and customization
- **Hot Swappable**: Switch themes without page reload
- **Extensible**: Easy to create new themes and extend existing ones

## Architecture

### Core Components

1. **ThemeService**: Main service class for theme management
2. **ThemeServiceProvider**: Laravel service provider for theme registration
3. **ThemeController**: API controller for theme operations
4. **Helper Functions**: Global helper functions for theme operations
5. **Blade Directives**: Custom Blade directives for theme-specific content

### Directory Structure

```
resources/views/themes/
├── modern/
│   ├── theme.json              # Theme metadata
│   ├── layout.blade.php        # Main layout file
│   ├── index.blade.php         # Homepage template
│   ├── partials/               # Reusable components
│   │   ├── header.blade.php
│   │   ├── footer.blade.php
│   │   └── theme-toggle.blade.php
│   └── sections/               # Page sections
│       ├── hero.blade.php
│       ├── about.blade.php
│       └── contact.blade.php
├── clark/                      # Existing theme
└── kross/                      # Existing theme

public/themes/
├── modern/
│   ├── css/
│   ├── js/
│   └── images/
├── clark/
└── kross/

config/themes/
├── modern.php                  # Theme configuration
├── clark.php
└── kross.php
```

## Installation & Setup

### 1. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Build assets
npm run build
```

### 2. Configure Environment

Add theme-related environment variables to your `.env` file:

```env
# Theme Configuration
DEFAULT_THEME=modern
HERO_NAME="Your Name"
HERO_TITLE="Your Title"
HERO_DESCRIPTION="Your description"
CONTACT_EMAIL="<EMAIL>"
CONTACT_PHONE="+1234567890"
```

### 3. Publish Assets

```bash
# Publish theme assets
php artisan vendor:publish --tag=theme-assets

# Clear cache
php artisan cache:clear
php artisan view:clear
```

## Usage

### Basic Theme Operations

```php
// Get current theme
$currentTheme = theme_service()->getCurrentTheme();

// Switch theme
theme_switch('modern');

// Check if theme supports feature
if (theme_supports('dark-mode')) {
    // Theme supports dark mode
}

// Get theme configuration
$heroTitle = theme_config_get('hero.title', 'Default Title');

// Generate theme asset URL
$cssUrl = theme_asset('css/app.css');
```

### Blade Directives

```blade
{{-- Check current theme --}}
@theme('modern')
    <p>This content only shows for the modern theme</p>
@endtheme

{{-- Theme asset --}}
<link rel="stylesheet" href="@themeAsset('css/app.css')">

{{-- Theme configuration --}}
<h1>@themeConfig('hero.title')</h1>

{{-- Dark mode content --}}
@darkMode
    <p>This content only shows in dark mode</p>
@enddarkMode
```

### API Endpoints

```http
# Get all themes
GET /api/theme

# Get current theme
GET /api/theme/current

# Switch theme
POST /api/theme/switch
{
    "theme": "modern"
}

# Toggle dark mode
POST /api/theme/toggle-dark-mode

# Get theme configuration
GET /api/theme/config?key=hero.title

# Check feature support
GET /api/theme/supports?feature=dark-mode

# Get theme metadata
GET /api/theme/{theme}/metadata

# Clear theme cache
DELETE /api/theme/cache
```

## Creating a New Theme

### 1. Create Theme Directory

```bash
mkdir -p resources/views/themes/mytheme
mkdir -p public/themes/mytheme/{css,js,images}
```

### 2. Create Theme Metadata

Create `resources/views/themes/mytheme/theme.json`:

```json
{
  "name": "mytheme",
  "title": "My Custom Theme",
  "description": "A custom theme for my portfolio",
  "version": "1.0.0",
  "author": "Your Name",
  "supports": [
    "dark-mode",
    "responsive",
    "accessibility"
  ]
}
```

### 3. Create Layout File

Create `resources/views/themes/mytheme/layout.blade.php`:

```blade
<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', config('app.name'))</title>
    
    <!-- Theme Assets -->
    <link rel="stylesheet" href="{{ theme_asset('css/app.css') }}">
</head>
<body>
    @include('themes.mytheme.partials.header')
    
    <main>
        @yield('content')
    </main>
    
    @include('themes.mytheme.partials.footer')
    
    <script src="{{ theme_asset('js/app.js') }}"></script>
</body>
</html>
```

### 4. Create Configuration File

Create `config/themes/mytheme.php`:

```php
<?php

return [
    'name' => 'mytheme',
    'title' => 'My Custom Theme',
    'hero' => [
        'title' => 'Welcome to My Portfolio',
        'description' => 'Custom theme description',
    ],
    'colors' => [
        'primary' => '#3b82f6',
        'secondary' => '#64748b',
    ],
];
```

## Customization

### Theme Configuration

Each theme can be customized through its configuration file in `config/themes/`. Common configuration options include:

- **Colors**: Primary, secondary, accent colors
- **Typography**: Font families and sizes
- **Layout**: Container widths, spacing
- **Features**: Enabled/disabled features
- **Content**: Default text content

### CSS Customization

Themes use Tailwind CSS for styling. You can customize:

1. **Tailwind Configuration**: Modify `tailwind.config.js`
2. **Custom CSS**: Add custom styles in theme CSS files
3. **CSS Variables**: Use CSS custom properties for dynamic theming

### JavaScript Customization

Themes include Alpine.js for interactivity. You can:

1. **Add Components**: Create new Alpine.js components
2. **Extend Functionality**: Add custom JavaScript modules
3. **API Integration**: Connect to theme APIs

## Performance Optimization

### Asset Optimization

- **CSS Minification**: Automatic in production
- **JavaScript Bundling**: Webpack optimization
- **Image Optimization**: Lazy loading and compression
- **Font Loading**: Optimized web font loading

### Caching

- **Theme Cache**: Themes are cached for performance
- **Asset Versioning**: Automatic cache busting
- **Browser Caching**: Proper cache headers

### Lazy Loading

- **Images**: Intersection Observer API
- **Components**: Dynamic component loading
- **Assets**: Progressive asset loading

## Accessibility

### WCAG 2.1 AA Compliance

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels
- **Color Contrast**: Sufficient contrast ratios
- **Focus Management**: Visible focus indicators

### Features

- **Skip Links**: Skip to main content
- **Alt Text**: Descriptive image alt text
- **Semantic HTML**: Proper HTML structure
- **Reduced Motion**: Respects user preferences

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Graceful degradation for older browsers

## Troubleshooting

### Common Issues

1. **Theme Not Loading**: Check theme name and file paths
2. **Assets Not Found**: Verify asset URLs and file existence
3. **Dark Mode Not Working**: Check theme support and JavaScript
4. **API Errors**: Verify CSRF tokens and authentication

### Debug Mode

Enable debug mode in `.env`:

```env
APP_DEBUG=true
THEME_DEBUG=true
```

### Cache Issues

Clear all caches:

```bash
php artisan cache:clear
php artisan view:clear
php artisan config:clear
php artisan route:clear
```

## Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Coding Standards

- Follow PSR-12 coding standards
- Use meaningful variable names
- Add proper documentation
- Write tests for new features

## License

This theme system is open-sourced software licensed under the [MIT license](LICENSE).
