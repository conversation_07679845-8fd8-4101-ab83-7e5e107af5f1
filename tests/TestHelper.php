<?php

namespace Tests;

/**
 * Test Helper Class
 * 
 * Provides utility methods and constants for testing
 */
class TestHelper
{
    /**
     * Expected companies in experience data
     */
    public const EXPECTED_COMPANIES = [
        'Content Fleet',
        'Imtilak Group', 
        'Imtilak Real Estate',
        'Binoplus'
    ];

    /**
     * Expected institutions in education data
     */
    public const EXPECTED_INSTITUTIONS = [
        'Sakarya University',
        'Istiklal High School'
    ];

    /**
     * Expected skill categories
     */
    public const EXPECTED_SKILL_CATEGORIES = [
        'backend',
        'frontend', 
        'database',
        'tools'
    ];

    /**
     * Expected backend technologies
     */
    public const EXPECTED_BACKEND_SKILLS = [
        'PHP',
        'Laravel',
        'C#',
        '.NET'
    ];

    /**
     * Expected frontend technologies
     */
    public const EXPECTED_FRONTEND_SKILLS = [
        'JavaScript',
        'Vue.js',
        'jQuery',
        'HTML5',
        'CSS3'
    ];

    /**
     * Expected database technologies
     */
    public const EXPECTED_DATABASE_SKILLS = [
        'MySQL',
        'PostgreSQL',
        'SQL Server',
        'SQLite'
    ];

    /**
     * API endpoints for testing
     */
    public const API_ENDPOINTS = [
        'resume' => '/api/resume',
        'experience' => '/api/resume/experience',
        'education' => '/api/resume/education',
        'skills' => '/api/resume/skills',
        'summary' => '/api/resume/summary',
        'search' => '/api/resume/search',
        'theme' => '/api/theme',
        'theme_current' => '/api/theme/current',
        'theme_config' => '/api/theme/config'
    ];

    /**
     * Required JSON response structure for resume endpoints
     */
    public const RESUME_RESPONSE_STRUCTURE = [
        'success',
        'data'
    ];

    /**
     * Required experience item structure
     */
    public const EXPERIENCE_ITEM_STRUCTURE = [
        'title',
        'company',
        'location',
        'type',
        'start_date',
        'current',
        'duration',
        'description',
        'responsibilities',
        'technologies',
        'achievements'
    ];

    /**
     * Required education item structure
     */
    public const EDUCATION_ITEM_STRUCTURE = [
        'title',
        'institution',
        'location',
        'degree',
        'field',
        'start_date',
        'end_date',
        'duration',
        'description'
    ];

    /**
     * Required skill item structure
     */
    public const SKILL_ITEM_STRUCTURE = [
        'name',
        'level',
        'years',
        'icon'
    ];

    /**
     * Maximum acceptable API response time in milliseconds
     */
    public const MAX_RESPONSE_TIME_MS = 1000;

    /**
     * Minimum expected experience years
     */
    public const MIN_EXPERIENCE_YEARS = 1;

    /**
     * Maximum reasonable experience years
     */
    public const MAX_EXPERIENCE_YEARS = 50;

    /**
     * Validate API response structure
     */
    public static function validateApiResponse(array $response, array $expectedStructure): bool
    {
        foreach ($expectedStructure as $key) {
            if (!array_key_exists($key, $response)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Validate experience item structure
     */
    public static function validateExperienceItem(array $item): bool
    {
        return self::validateApiResponse($item, self::EXPERIENCE_ITEM_STRUCTURE);
    }

    /**
     * Validate education item structure
     */
    public static function validateEducationItem(array $item): bool
    {
        return self::validateApiResponse($item, self::EDUCATION_ITEM_STRUCTURE);
    }

    /**
     * Validate skill item structure
     */
    public static function validateSkillItem(array $item): bool
    {
        return self::validateApiResponse($item, self::SKILL_ITEM_STRUCTURE);
    }

    /**
     * Check if response time is acceptable
     */
    public static function isResponseTimeAcceptable(float $responseTimeMs): bool
    {
        return $responseTimeMs <= self::MAX_RESPONSE_TIME_MS;
    }

    /**
     * Check if experience years value is reasonable
     */
    public static function isExperienceYearsReasonable(int $years): bool
    {
        return $years >= self::MIN_EXPERIENCE_YEARS && $years <= self::MAX_EXPERIENCE_YEARS;
    }

    /**
     * Get test search queries
     */
    public static function getTestSearchQueries(): array
    {
        return [
            'php' => 'PHP',
            'university' => 'University',
            'javascript' => 'JavaScript',
            'laravel' => 'Laravel',
            'mysql' => 'MySQL',
            'developer' => 'Developer'
        ];
    }

    /**
     * Get invalid test data
     */
    public static function getInvalidTestData(): array
    {
        return [
            'invalid_experience_index' => 999,
            'invalid_education_index' => 999,
            'invalid_skills_category' => 'invalid-category',
            'invalid_theme' => 'invalid-theme',
            'empty_search_query' => '',
            'null_search_query' => null
        ];
    }

    /**
     * Generate test duration string
     */
    public static function generateTestDuration(int $years, int $months = 0): string
    {
        $duration = '';
        if ($years > 0) {
            $duration .= $years . ' year' . ($years > 1 ? 's' : '');
        }
        if ($months > 0) {
            if ($duration) $duration .= ' ';
            $duration .= $months . ' month' . ($months > 1 ? 's' : '');
        }
        return $duration ?: '1 month';
    }

    /**
     * Mock experience data for testing
     */
    public static function getMockExperienceData(): array
    {
        return [
            [
                'title' => 'Test Developer',
                'company' => 'Test Company',
                'location' => 'Test City',
                'type' => 'Full-time',
                'start_date' => '2020-01-01',
                'end_date' => '2022-01-01',
                'current' => false,
                'duration' => '2 years',
                'description' => 'Test description',
                'responsibilities' => ['Test responsibility'],
                'technologies' => ['PHP', 'Laravel'],
                'achievements' => ['Test achievement']
            ]
        ];
    }

    /**
     * Mock education data for testing
     */
    public static function getMockEducationData(): array
    {
        return [
            [
                'title' => 'Test Degree',
                'institution' => 'Test University',
                'location' => 'Test City',
                'degree' => 'Bachelor\'s',
                'field' => 'Computer Science',
                'start_date' => '2015-09-01',
                'end_date' => '2019-06-01',
                'duration' => '2015 - 2019',
                'description' => 'Test education description'
            ]
        ];
    }

    /**
     * Mock skills data for testing
     */
    public static function getMockSkillsData(): array
    {
        return [
            'backend' => [
                'title' => 'Backend Development',
                'description' => 'Server-side technologies',
                'skills' => [
                    [
                        'name' => 'PHP',
                        'level' => 95,
                        'years' => 7,
                        'icon' => 'php'
                    ]
                ]
            ]
        ];
    }
}
