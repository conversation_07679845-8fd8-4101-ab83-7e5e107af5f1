<?php

namespace Tests\Unit;

use Tests\TestCase;
use Carbon\Carbon;

class HelperFunctionsTest extends TestCase
{
    /**
     * Test that get_experience_data returns array of experiences.
     */
    public function test_get_experience_data_returns_array()
    {
        $experiences = get_experience_data();

        $this->assertIsArray($experiences);
        $this->assertGreaterThan(0, count($experiences));
        
        // Test structure of first experience
        if (count($experiences) > 0) {
            $firstExperience = $experiences[0];
            $this->assertArrayHasKey('title', $firstExperience);
            $this->assertArrayHasKey('company', $firstExperience);
            $this->assertArrayHasKey('location', $firstExperience);
            $this->assertArrayHasKey('description', $firstExperience);
            $this->assertArrayHasKey('technologies', $firstExperience);
        }
    }

    /**
     * Test that get_education_data returns array of education records.
     */
    public function test_get_education_data_returns_array()
    {
        $education = get_education_data();

        $this->assertIsArray($education);
        $this->assertGreater<PERSON>han(0, count($education));
        
        // Test structure of first education record
        if (count($education) > 0) {
            $firstEducation = $education[0];
            $this->assertArrayHasKey('title', $firstEducation);
            $this->assertArrayHasKey('institution', $firstEducation);
            $this->assertArrayHasKey('location', $firstEducation);
            $this->assertArrayHasKey('description', $firstEducation);
        }
    }

    /**
     * Test that get_certifications_data returns array of certifications.
     */
    public function test_get_certifications_data_returns_array()
    {
        $certifications = get_certifications_data();

        $this->assertIsArray($certifications);
        $this->assertGreaterThan(0, count($certifications));
        
        // Test structure of first certification
        if (count($certifications) > 0) {
            $firstCertification = $certifications[0];
            $this->assertArrayHasKey('title', $firstCertification);
            $this->assertArrayHasKey('provider', $firstCertification);
            $this->assertArrayHasKey('date', $firstCertification);
        }
    }

    /**
     * Test that get_skills_by_category returns categorized skills.
     */
    public function test_get_skills_by_category_returns_categorized_skills()
    {
        $skills = get_skills_by_category();

        $this->assertIsArray($skills);
        $this->assertGreaterThan(0, count($skills));
        
        // Test that expected categories exist
        $this->assertArrayHasKey('backend', $skills);
        $this->assertArrayHasKey('frontend', $skills);
        $this->assertArrayHasKey('database', $skills);
        $this->assertArrayHasKey('tools', $skills);
        
        // Test structure of backend category
        $backend = $skills['backend'];
        $this->assertArrayHasKey('title', $backend);
        $this->assertArrayHasKey('description', $backend);
        $this->assertArrayHasKey('skills', $backend);
        $this->assertIsArray($backend['skills']);
        
        // Test structure of individual skill
        if (count($backend['skills']) > 0) {
            $firstSkill = $backend['skills'][0];
            $this->assertArrayHasKey('name', $firstSkill);
            $this->assertArrayHasKey('level', $firstSkill);
            $this->assertArrayHasKey('years', $firstSkill);
            $this->assertArrayHasKey('icon', $firstSkill);
        }
    }

    /**
     * Test that get_soft_skills returns array of soft skills.
     */
    public function test_get_soft_skills_returns_array()
    {
        $softSkills = get_soft_skills();

        $this->assertIsArray($softSkills);
        $this->assertGreaterThan(0, count($softSkills));
        
        // Test structure of first soft skill
        if (count($softSkills) > 0) {
            $firstSkill = $softSkills[0];
            $this->assertArrayHasKey('name', $firstSkill);
            $this->assertArrayHasKey('description', $firstSkill);
        }
    }

    /**
     * Test that get_resume_data returns complete resume configuration.
     */
    public function test_get_resume_data_returns_complete_config()
    {
        $resumeData = get_resume_data();

        $this->assertIsArray($resumeData);
        $this->assertArrayHasKey('title', $resumeData);
        $this->assertArrayHasKey('subtitle', $resumeData);
        $this->assertArrayHasKey('sections', $resumeData);
        $this->assertArrayHasKey('contact_info', $resumeData);
    }

    /**
     * Test format_experience_duration function with various date ranges.
     */
    public function test_format_experience_duration_with_date_ranges()
    {
        // Test with start and end date
        $startDate = '2020-01-01';
        $endDate = '2022-06-01';
        $duration = format_experience_duration($startDate, $endDate);
        
        $this->assertIsString($duration);
        $this->assertStringContainsString('year', $duration);
        
        // Test with only start date (current job)
        $currentDuration = format_experience_duration($startDate);
        $this->assertIsString($currentDuration);
        
        // Test with recent start date
        $recentStart = Carbon::now()->subMonths(6)->format('Y-m-d');
        $recentDuration = format_experience_duration($recentStart);
        $this->assertIsString($recentDuration);
        $this->assertStringContainsString('month', $recentDuration);
    }

    /**
     * Test get_total_experience_years returns numeric value.
     */
    public function test_get_total_experience_years_returns_numeric()
    {
        $years = get_total_experience_years();

        $this->assertIsNumeric($years);
        $this->assertGreaterThan(0, $years);
    }

    /**
     * Test get_skill_icon_path function.
     */
    public function test_get_skill_icon_path_function()
    {
        // Test with common skill
        $phpIconPath = get_skill_icon_path('php');
        // Should return null if file doesn't exist, or string if it does
        $this->assertTrue(is_null($phpIconPath) || is_string($phpIconPath));
        
        // Test with invalid skill
        $invalidIconPath = get_skill_icon_path('invalid-skill-name');
        $this->assertNull($invalidIconPath);
    }

    /**
     * Test theme configuration functions.
     */
    public function test_theme_config_functions()
    {
        // Test theme_config_get function
        $heroConfig = theme_config_get('hero');
        $this->assertIsArray($heroConfig);
        
        // Test with default value
        $nonExistentConfig = theme_config_get('non_existent_key', 'default_value');
        $this->assertEquals('default_value', $nonExistentConfig);
        
        // Test nested config access
        $heroTitle = theme_config_get('hero.title');
        $this->assertIsString($heroTitle);
    }

    /**
     * Test that all helper functions are properly defined.
     */
    public function test_all_helper_functions_are_defined()
    {
        $helperFunctions = [
            'get_experience_data',
            'get_education_data',
            'get_certifications_data',
            'get_skills_by_category',
            'get_soft_skills',
            'get_resume_data',
            'format_experience_duration',
            'get_total_experience_years',
            'get_skill_icon_path',
            'theme_config_get',
            'get_theme_name'
        ];

        foreach ($helperFunctions as $function) {
            $this->assertTrue(
                function_exists($function),
                "Helper function '{$function}' is not defined"
            );
        }
    }

    /**
     * Test that experience data contains expected companies.
     */
    public function test_experience_data_contains_expected_companies()
    {
        $experiences = get_experience_data();
        $companies = array_column($experiences, 'company');

        $expectedCompanies = [
            'Content Fleet',
            'Imtilak Group',
            'Imtilak Real Estate',
            'Binoplus'
        ];

        foreach ($expectedCompanies as $company) {
            $this->assertContains(
                $company,
                $companies,
                "Expected company '{$company}' not found in experience data"
            );
        }
    }

    /**
     * Test that education data contains expected institutions.
     */
    public function test_education_data_contains_expected_institutions()
    {
        $education = get_education_data();
        $institutions = array_column($education, 'institution');

        $expectedInstitutions = [
            'Sakarya University',
            'Istiklal High School'
        ];

        foreach ($expectedInstitutions as $institution) {
            $this->assertContains(
                $institution,
                $institutions,
                "Expected institution '{$institution}' not found in education data"
            );
        }
    }

    /**
     * Test that skills data contains expected technologies.
     */
    public function test_skills_data_contains_expected_technologies()
    {
        $skills = get_skills_by_category();
        
        // Check backend skills
        $backendSkills = array_column($skills['backend']['skills'], 'name');
        $this->assertContains('PHP', $backendSkills);
        $this->assertContains('Laravel', $backendSkills);
        
        // Check frontend skills
        $frontendSkills = array_column($skills['frontend']['skills'], 'name');
        $this->assertContains('JavaScript', $frontendSkills);
        $this->assertContains('Vue.js', $frontendSkills);
        
        // Check database skills
        $databaseSkills = array_column($skills['database']['skills'], 'name');
        $this->assertContains('MySQL', $databaseSkills);
        $this->assertContains('PostgreSQL', $databaseSkills);
    }
}
