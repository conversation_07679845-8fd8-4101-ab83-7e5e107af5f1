<?php

namespace Tests\Unit;

use Tests\TestCase;

class ApplicationUnitTest extends TestCase
{
    /**
     * Test that basic PHP functionality works.
     */
    public function test_basic_php_functionality()
    {
        $this->assertTrue(true);
        $this->assertEquals(2, 1 + 1);
        $this->assertIsArray([]);
        $this->assertIsString('test');
    }

    /**
     * Test that Laravel helper functions are available.
     */
    public function test_laravel_helpers_are_available()
    {
        $this->assertTrue(function_exists('app'));
        $this->assertTrue(function_exists('config'));
        $this->assertTrue(function_exists('env'));
        $this->assertTrue(function_exists('route'));
    }

    /**
     * Test that custom helper functions are loaded.
     */
    public function test_custom_helpers_are_loaded()
    {
        $this->assertTrue(function_exists('theme_config_get'));
        $this->assertTrue(function_exists('get_experience_data'));
        $this->assertTrue(function_exists('get_education_data'));
        $this->assertTrue(function_exists('get_skills_by_category'));
    }

    /**
     * Test that configuration files can be loaded.
     */
    public function test_configuration_files_can_be_loaded()
    {
        $appConfig = config('app');
        $this->assertIsArray($appConfig);
        $this->assertArrayHasKey('name', $appConfig);
        $this->assertArrayHasKey('env', $appConfig);
    }
}
