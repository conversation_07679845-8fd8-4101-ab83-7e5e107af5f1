<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Controllers\Api\ResumeController;
use Illuminate\Http\JsonResponse;

class ResumeControllerTest extends TestCase
{
    protected $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new ResumeController();
    }

    /**
     * Test that the index method returns proper JSON response.
     */
    public function test_index_method_returns_proper_response()
    {
        $response = $this->controller->index();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('experience', $data['data']);
        $this->assertArrayHasKey('education', $data['data']);
        $this->assertArrayHas<PERSON>ey('skills', $data['data']);
    }

    /**
     * Test that the experience method returns formatted experience data.
     */
    public function test_experience_method_returns_formatted_data()
    {
        $response = $this->controller->experience();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertIsArray($data['data']);

        // Check that formatted_duration is added
        if (count($data['data']) > 0) {
            $firstExperience = $data['data'][0];
            $this->assertArrayHasKey('formatted_duration', $firstExperience);
            $this->assertIsString($firstExperience['formatted_duration']);
        }
    }

    /**
     * Test that the education method returns education and certifications.
     */
    public function test_education_method_returns_complete_data()
    {
        $response = $this->controller->education();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('education', $data['data']);
        $this->assertArrayHasKey('certifications', $data['data']);
        $this->assertIsArray($data['data']['education']);
        $this->assertIsArray($data['data']['certifications']);
    }

    /**
     * Test that the skills method returns technical and soft skills.
     */
    public function test_skills_method_returns_categorized_skills()
    {
        $response = $this->controller->skills();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('technical_skills', $data['data']);
        $this->assertArrayHasKey('soft_skills', $data['data']);
        $this->assertIsArray($data['data']['technical_skills']);
        $this->assertIsArray($data['data']['soft_skills']);
    }

    /**
     * Test that the summary method returns summary data.
     */
    public function test_summary_method_returns_summary_data()
    {
        $response = $this->controller->summary();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('summary', $data['data']);
        $this->assertArrayHasKey('contact_info', $data['data']);
        $this->assertArrayHasKey('total_experience_years', $data['data']);
        $this->assertIsNumeric($data['data']['total_experience_years']);
    }

    /**
     * Test that getExperience method returns specific experience.
     */
    public function test_get_experience_method_returns_specific_experience()
    {
        $response = $this->controller->getExperience(0);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('title', $data['data']);
        $this->assertArrayHasKey('company', $data['data']);
        $this->assertArrayHasKey('formatted_duration', $data['data']);
    }

    /**
     * Test that getExperience method returns 404 for invalid index.
     */
    public function test_get_experience_method_returns_404_for_invalid_index()
    {
        $response = $this->controller->getExperience(999);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Experience not found', $data['message']);
    }

    /**
     * Test that getEducation method returns specific education.
     */
    public function test_get_education_method_returns_specific_education()
    {
        $response = $this->controller->getEducation(0);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('title', $data['data']);
        $this->assertArrayHasKey('institution', $data['data']);
    }

    /**
     * Test that getEducation method returns 404 for invalid index.
     */
    public function test_get_education_method_returns_404_for_invalid_index()
    {
        $response = $this->controller->getEducation(999);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Education record not found', $data['message']);
    }

    /**
     * Test that getSkillsByCategory method returns specific category.
     */
    public function test_get_skills_by_category_method_returns_specific_category()
    {
        $response = $this->controller->getSkillsByCategory('backend');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('title', $data['data']);
        $this->assertArrayHasKey('skills', $data['data']);
        $this->assertIsArray($data['data']['skills']);
    }

    /**
     * Test that getSkillsByCategory method returns 404 for invalid category.
     */
    public function test_get_skills_by_category_method_returns_404_for_invalid_category()
    {
        $response = $this->controller->getSkillsByCategory('invalid-category');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Skill category not found', $data['message']);
    }

    /**
     * Test search method with valid query.
     */
    public function test_search_method_with_valid_query()
    {
        $request = new \Illuminate\Http\Request(['q' => 'PHP']);
        $response = $this->controller->search($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('query', $data);
        $this->assertEquals('PHP', $data['query']);
        $this->assertArrayHasKey('experience', $data['data']);
        $this->assertArrayHasKey('education', $data['data']);
        $this->assertArrayHasKey('skills', $data['data']);
    }

    /**
     * Test search method without query parameter.
     */
    public function test_search_method_without_query_returns_400()
    {
        $request = new \Illuminate\Http\Request();
        $response = $this->controller->search($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Search query is required', $data['message']);
    }

    /**
     * Test search method with empty query.
     */
    public function test_search_method_with_empty_query_returns_400()
    {
        $request = new \Illuminate\Http\Request(['q' => '']);
        $response = $this->controller->search($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());

        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Search query is required', $data['message']);
    }

    /**
     * Test that all controller methods return proper JSON structure.
     */
    public function test_all_methods_return_proper_json_structure()
    {
        $methods = [
            'index',
            'experience',
            'education',
            'skills',
            'summary'
        ];

        foreach ($methods as $method) {
            $response = $this->controller->$method();
            $data = json_decode($response->getContent(), true);

            $this->assertArrayHasKey('success', $data, "Method {$method} missing 'success' key");
            $this->assertArrayHasKey('data', $data, "Method {$method} missing 'data' key");
            $this->assertTrue($data['success'], "Method {$method} returned success=false");
        }
    }

    /**
     * Test that controller handles missing helper functions gracefully.
     */
    public function test_controller_handles_missing_data_gracefully()
    {
        // This test assumes that if helper functions return empty arrays,
        // the controller should still return valid JSON responses

        $response = $this->controller->index();
        $data = json_decode($response->getContent(), true);

        $this->assertTrue($data['success']);
        $this->assertIsArray($data['data']);
    }
}
