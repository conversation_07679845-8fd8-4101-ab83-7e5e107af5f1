<?php

namespace Tests\Unit;

use Tests\TestCase;

class ThemeSystemTest extends TestCase
{
    /**
     * Test that theme service is properly initialized.
     */
    public function test_theme_service_is_initialized()
    {
        $themeService = theme_service();
        
        $this->assertNotNull($themeService);
        $this->assertIsObject($themeService);
    }

    /**
     * Test that current theme can be retrieved.
     */
    public function test_current_theme_can_be_retrieved()
    {
        $currentTheme = get_theme_name();
        
        $this->assertIsString($currentTheme);
        $this->assertNotEmpty($currentTheme);
    }

    /**
     * Test that available themes can be retrieved.
     */
    public function test_available_themes_can_be_retrieved()
    {
        $availableThemes = theme_get_available();
        
        $this->assertIsArray($availableThemes);
        $this->assertGreaterThan(0, count($availableThemes));
        
        // Check that modern theme exists
        $themeNames = array_column($availableThemes, 'name');
        $this->assertContains('modern', $themeNames);
    }

    /**
     * Test that theme configuration can be accessed.
     */
    public function test_theme_configuration_can_be_accessed()
    {
        $config = theme_config_get();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('hero', $config);
        $this->assertArrayHasKey('about', $config);
        $this->assertArrayHasKey('experience', $config);
        $this->assertArrayHasKey('education', $config);
        $this->assertArrayHasKey('skills', $config);
        $this->assertArrayHasKey('resume', $config);
    }

    /**
     * Test that specific theme configuration sections can be accessed.
     */
    public function test_specific_theme_config_sections()
    {
        // Test hero section
        $heroConfig = theme_config_get('hero');
        $this->assertIsArray($heroConfig);
        $this->assertArrayHasKey('title', $heroConfig);
        $this->assertArrayHasKey('subtitle', $heroConfig);
        
        // Test about section
        $aboutConfig = theme_config_get('about');
        $this->assertIsArray($aboutConfig);
        $this->assertArrayHasKey('title', $aboutConfig);
        $this->assertArrayHasKey('description', $aboutConfig);
        
        // Test experience section
        $experienceConfig = theme_config_get('experience');
        $this->assertIsArray($experienceConfig);
        $this->assertArrayHasKey('title', $experienceConfig);
        $this->assertArrayHasKey('items', $experienceConfig);
        
        // Test skills section
        $skillsConfig = theme_config_get('skills');
        $this->assertIsArray($skillsConfig);
        $this->assertArrayHasKey('title', $skillsConfig);
        $this->assertArrayHasKey('categories', $skillsConfig);
    }

    /**
     * Test that theme configuration has proper structure for modern theme.
     */
    public function test_modern_theme_config_structure()
    {
        $config = theme_config_get();
        
        // Test hero section structure
        $hero = $config['hero'];
        $this->assertArrayHasKey('name', $hero);
        $this->assertArrayHasKey('title', $hero);
        $this->assertArrayHasKey('subtitle', $hero);
        $this->assertArrayHasKey('description', $hero);
        $this->assertArrayHasKey('cta_buttons', $hero);
        
        // Test about section structure
        $about = $config['about'];
        $this->assertArrayHasKey('title', $about);
        $this->assertArrayHasKey('subtitle', $about);
        $this->assertArrayHasKey('description', $about);
        $this->assertArrayHasKey('years_experience', $about);
        $this->assertArrayHasKey('projects_completed', $about);
        $this->assertArrayHasKey('happy_clients', $about);
        
        // Test experience section structure
        $experience = $config['experience'];
        $this->assertArrayHasKey('title', $experience);
        $this->assertArrayHasKey('subtitle', $experience);
        $this->assertArrayHasKey('items', $experience);
        $this->assertIsArray($experience['items']);
        
        // Test education section structure
        $education = $config['education'];
        $this->assertArrayHasKey('title', $education);
        $this->assertArrayHasKey('subtitle', $education);
        $this->assertArrayHasKey('items', $education);
        $this->assertArrayHasKey('certifications', $education);
        
        // Test skills section structure
        $skills = $config['skills'];
        $this->assertArrayHasKey('title', $skills);
        $this->assertArrayHasKey('categories', $skills);
        $this->assertArrayHasKey('soft_skills', $skills);
    }

    /**
     * Test that experience items have proper structure.
     */
    public function test_experience_items_structure()
    {
        $experiences = theme_config_get('experience.items');
        
        $this->assertIsArray($experiences);
        $this->assertGreaterThan(0, count($experiences));
        
        foreach ($experiences as $experience) {
            $this->assertArrayHasKey('title', $experience);
            $this->assertArrayHasKey('company', $experience);
            $this->assertArrayHasKey('location', $experience);
            $this->assertArrayHasKey('type', $experience);
            $this->assertArrayHasKey('start_date', $experience);
            $this->assertArrayHasKey('current', $experience);
            $this->assertArrayHasKey('description', $experience);
            $this->assertArrayHasKey('responsibilities', $experience);
            $this->assertArrayHasKey('technologies', $experience);
            $this->assertArrayHasKey('achievements', $experience);
            
            // Validate data types
            $this->assertIsString($experience['title']);
            $this->assertIsString($experience['company']);
            $this->assertIsString($experience['location']);
            $this->assertIsBool($experience['current']);
            $this->assertIsArray($experience['responsibilities']);
            $this->assertIsArray($experience['technologies']);
            $this->assertIsArray($experience['achievements']);
        }
    }

    /**
     * Test that education items have proper structure.
     */
    public function test_education_items_structure()
    {
        $education = theme_config_get('education.items');
        
        $this->assertIsArray($education);
        $this->assertGreaterThan(0, count($education));
        
        foreach ($education as $edu) {
            $this->assertArrayHasKey('title', $edu);
            $this->assertArrayHasKey('institution', $edu);
            $this->assertArrayHasKey('location', $edu);
            $this->assertArrayHasKey('degree', $edu);
            $this->assertArrayHasKey('field', $edu);
            $this->assertArrayHasKey('start_date', $edu);
            $this->assertArrayHasKey('end_date', $edu);
            $this->assertArrayHasKey('description', $edu);
            
            // Validate data types
            $this->assertIsString($edu['title']);
            $this->assertIsString($edu['institution']);
            $this->assertIsString($edu['location']);
            $this->assertIsString($edu['description']);
        }
    }

    /**
     * Test that skills categories have proper structure.
     */
    public function test_skills_categories_structure()
    {
        $skillsCategories = theme_config_get('skills.categories');
        
        $this->assertIsArray($skillsCategories);
        $this->assertGreaterThan(0, count($skillsCategories));
        
        $expectedCategories = ['backend', 'frontend', 'database', 'tools'];
        
        foreach ($expectedCategories as $category) {
            $this->assertArrayHasKey($category, $skillsCategories);
            
            $categoryData = $skillsCategories[$category];
            $this->assertArrayHasKey('title', $categoryData);
            $this->assertArrayHasKey('description', $categoryData);
            $this->assertArrayHasKey('skills', $categoryData);
            $this->assertIsArray($categoryData['skills']);
            
            // Test individual skills structure
            foreach ($categoryData['skills'] as $skill) {
                $this->assertArrayHasKey('name', $skill);
                $this->assertArrayHasKey('level', $skill);
                $this->assertArrayHasKey('years', $skill);
                $this->assertArrayHasKey('icon', $skill);
                
                // Validate data types and ranges
                $this->assertIsString($skill['name']);
                $this->assertIsNumeric($skill['level']);
                $this->assertIsNumeric($skill['years']);
                $this->assertGreaterThanOrEqual(0, $skill['level']);
                $this->assertLessThanOrEqual(100, $skill['level']);
                $this->assertGreaterThan(0, $skill['years']);
            }
        }
    }

    /**
     * Test that certifications have proper structure.
     */
    public function test_certifications_structure()
    {
        $certifications = theme_config_get('education.certifications');
        
        $this->assertIsArray($certifications);
        $this->assertGreaterThan(0, count($certifications));
        
        foreach ($certifications as $cert) {
            $this->assertArrayHasKey('title', $cert);
            $this->assertArrayHasKey('provider', $cert);
            $this->assertArrayHasKey('date', $cert);
            $this->assertArrayHasKey('description', $cert);
            
            // Validate data types
            $this->assertIsString($cert['title']);
            $this->assertIsString($cert['provider']);
            $this->assertIsString($cert['date']);
            $this->assertIsString($cert['description']);
        }
    }

    /**
     * Test that resume configuration has proper structure.
     */
    public function test_resume_config_structure()
    {
        $resumeConfig = theme_config_get('resume');
        
        $this->assertIsArray($resumeConfig);
        $this->assertArrayHasKey('title', $resumeConfig);
        $this->assertArrayHasKey('subtitle', $resumeConfig);
        $this->assertArrayHasKey('description', $resumeConfig);
        $this->assertArrayHasKey('show_download_button', $resumeConfig);
        $this->assertArrayHasKey('sections', $resumeConfig);
        $this->assertArrayHasKey('contact_info', $resumeConfig);
        
        // Test sections structure
        $sections = $resumeConfig['sections'];
        $this->assertArrayHasKey('summary', $sections);
        $this->assertArrayHasKey('experience', $sections);
        $this->assertArrayHasKey('education', $sections);
        $this->assertArrayHasKey('skills', $sections);
        
        // Test contact info structure
        $contactInfo = $resumeConfig['contact_info'];
        $this->assertArrayHasKey('show_on_resume', $contactInfo);
        $this->assertArrayHasKey('items', $contactInfo);
        $this->assertIsArray($contactInfo['items']);
    }

    /**
     * Test that theme configuration values are reasonable.
     */
    public function test_theme_config_values_are_reasonable()
    {
        // Test experience years
        $yearsExperience = theme_config_get('about.years_experience');
        $this->assertIsNumeric($yearsExperience);
        $this->assertGreaterThan(0, $yearsExperience);
        $this->assertLessThan(50, $yearsExperience); // Reasonable upper limit
        
        // Test projects completed
        $projectsCompleted = theme_config_get('about.projects_completed');
        $this->assertIsNumeric($projectsCompleted);
        $this->assertGreaterThan(0, $projectsCompleted);
        
        // Test happy clients
        $happyClients = theme_config_get('about.happy_clients');
        $this->assertIsNumeric($happyClients);
        $this->assertGreaterThanOrEqual(0, $happyClients);
    }
}
