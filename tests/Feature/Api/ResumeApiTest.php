<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ResumeApiTest extends TestCase
{
    /**
     * Test that the resume API index endpoint returns successful response.
     */
    public function test_resume_api_index_returns_successful_response()
    {
        $response = $this->getJson('/api/resume');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'experience',
                         'education',
                         'certifications',
                         'skills',
                         'soft_skills',
                         'resume_config',
                         'total_experience_years'
                     ]
                 ]);
    }

    /**
     * Test that the experience API endpoint returns proper data structure.
     */
    public function test_experience_api_returns_proper_structure()
    {
        $response = $this->getJson('/api/resume/experience');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         '*' => [
                             'title',
                             'company',
                             'location',
                             'type',
                             'start_date',
                             'current',
                             'duration',
                             'description',
                             'responsibilities',
                             'technologies',
                             'achievements'
                         ]
                     ]
                 ]);

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertGreaterThan(0, count($data));
    }

    /**
     * Test that the education API endpoint returns proper data structure.
     */
    public function test_education_api_returns_proper_structure()
    {
        $response = $this->getJson('/api/resume/education');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'education' => [
                             '*' => [
                                 'title',
                                 'institution',
                                 'location',
                                 'degree',
                                 'field',
                                 'start_date',
                                 'end_date',
                                 'duration',
                                 'description'
                             ]
                         ],
                         'certifications' => [
                             '*' => [
                                 'title',
                                 'provider',
                                 'date',
                                 'description'
                             ]
                         ]
                     ]
                 ]);
    }

    /**
     * Test that the skills API endpoint returns categorized skills.
     */
    public function test_skills_api_returns_categorized_skills()
    {
        $response = $this->getJson('/api/resume/skills');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'technical_skills' => [
                             '*' => [
                                 'title',
                                 'description',
                                 'skills' => [
                                     '*' => [
                                         'name',
                                         'level',
                                         'years',
                                         'icon'
                                     ]
                                 ]
                             ]
                         ],
                         'soft_skills' => [
                             '*' => [
                                 'name',
                                 'description'
                             ]
                         ]
                     ]
                 ]);

        $data = $response->json('data.technical_skills');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('backend', $data);
        $this->assertArrayHasKey('frontend', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('tools', $data);
    }

    /**
     * Test that the summary API endpoint returns proper summary data.
     */
    public function test_summary_api_returns_proper_data()
    {
        $response = $this->getJson('/api/resume/summary');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'summary',
                         'contact_info',
                         'total_experience_years',
                         'total_projects',
                         'happy_clients'
                     ]
                 ]);

        $data = $response->json('data');
        $this->assertIsNumeric($data['total_experience_years']);
        $this->assertGreaterThan(0, $data['total_experience_years']);
    }

    /**
     * Test that individual experience endpoint returns specific experience.
     */
    public function test_individual_experience_endpoint()
    {
        $response = $this->getJson('/api/resume/experience/0');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'title',
                         'company',
                         'location',
                         'description',
                         'technologies',
                         'formatted_duration'
                     ]
                 ]);
    }

    /**
     * Test that invalid experience index returns 404.
     */
    public function test_invalid_experience_index_returns_404()
    {
        $response = $this->getJson('/api/resume/experience/999');

        $response->assertStatus(404)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Experience not found'
                 ]);
    }

    /**
     * Test that individual education endpoint returns specific education.
     */
    public function test_individual_education_endpoint()
    {
        $response = $this->getJson('/api/resume/education/0');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'title',
                         'institution',
                         'location',
                         'description'
                     ]
                 ]);
    }

    /**
     * Test that skills by category endpoint returns specific category.
     */
    public function test_skills_by_category_endpoint()
    {
        $response = $this->getJson('/api/resume/skills/backend');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'title',
                         'description',
                         'skills' => [
                             '*' => [
                                 'name',
                                 'level',
                                 'years',
                                 'icon'
                             ]
                         ]
                     ]
                 ]);
    }

    /**
     * Test that invalid skills category returns 404.
     */
    public function test_invalid_skills_category_returns_404()
    {
        $response = $this->getJson('/api/resume/skills/invalid-category');

        $response->assertStatus(404)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Skill category not found'
                 ]);
    }

    /**
     * Test search functionality with valid query.
     */
    public function test_search_with_valid_query()
    {
        $response = $this->getJson('/api/resume/search?q=PHP');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'experience',
                         'education',
                         'skills'
                     ],
                     'query'
                 ]);
    }

    /**
     * Test search functionality without query parameter.
     */
    public function test_search_without_query_returns_400()
    {
        $response = $this->getJson('/api/resume/search');

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Search query is required'
                 ]);
    }
}
