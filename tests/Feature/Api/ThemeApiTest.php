<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ThemeApiTest extends TestCase
{
    /**
     * Test that the theme API index endpoint returns available themes.
     */
    public function test_theme_api_index_returns_available_themes()
    {
        $response = $this->getJson('/api/theme');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         '*' => [
                             'name',
                             'display_name',
                             'description',
                             'version',
                             'author'
                         ]
                     ]
                 ]);
    }

    /**
     * Test that the current theme endpoint returns current theme info.
     */
    public function test_current_theme_endpoint_returns_theme_info()
    {
        $response = $this->getJson('/api/theme/current');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'name',
                         'display_name',
                         'description',
                         'version',
                         'author',
                         'config'
                     ]
                 ]);
    }

    /**
     * Test that the theme config endpoint returns configuration.
     */
    public function test_theme_config_endpoint_returns_configuration()
    {
        $response = $this->getJson('/api/theme/config');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'hero',
                         'about',
                         'experience',
                         'education',
                         'skills',
                         'resume',
                         'portfolio',
                         'services',
                         'contact'
                     ]
                 ]);
    }

    /**
     * Test that theme supports endpoint returns supported features.
     */
    public function test_theme_supports_endpoint_returns_features()
    {
        $response = $this->getJson('/api/theme/supports');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'dark_mode',
                         'responsive',
                         'animations',
                         'portfolio',
                         'blog',
                         'contact_form'
                     ]
                 ]);
    }

    /**
     * Test that specific theme metadata endpoint works.
     */
    public function test_theme_metadata_endpoint_returns_metadata()
    {
        $response = $this->getJson('/api/theme/modern/metadata');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'name',
                         'display_name',
                         'description',
                         'version',
                         'author',
                         'supports'
                     ]
                 ]);
    }

    /**
     * Test that invalid theme metadata returns 404.
     */
    public function test_invalid_theme_metadata_returns_404()
    {
        $response = $this->getJson('/api/theme/invalid-theme/metadata');

        $response->assertStatus(404)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Theme not found'
                 ]);
    }

    /**
     * Test that theme assets endpoint returns asset information.
     */
    public function test_theme_assets_endpoint_returns_assets()
    {
        $response = $this->getJson('/api/theme/modern/assets');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'css',
                         'js',
                         'images'
                     ]
                 ]);
    }

    /**
     * Test theme switching functionality.
     */
    public function test_theme_switch_endpoint()
    {
        $response = $this->postJson('/api/theme/switch', [
            'theme' => 'modern'
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'current_theme',
                         'previous_theme'
                     ]
                 ]);
    }

    /**
     * Test theme switching with invalid theme.
     */
    public function test_theme_switch_with_invalid_theme()
    {
        $response = $this->postJson('/api/theme/switch', [
            'theme' => 'invalid-theme'
        ]);

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Invalid theme specified'
                 ]);
    }

    /**
     * Test dark mode toggle functionality.
     */
    public function test_dark_mode_toggle_endpoint()
    {
        $response = $this->postJson('/api/theme/toggle-dark-mode');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'dark_mode_enabled'
                     ]
                 ]);
    }

    /**
     * Test cache clearing functionality.
     */
    public function test_cache_clear_endpoint()
    {
        $response = $this->deleteJson('/api/theme/cache');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Theme cache cleared successfully'
                 ]);
    }
}
