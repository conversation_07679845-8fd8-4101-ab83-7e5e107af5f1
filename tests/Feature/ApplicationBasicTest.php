<?php

namespace Tests\Feature;

use Tests\TestCase;

class ApplicationBasicTest extends TestCase
{
    /**
     * Test that the application returns a successful response.
     */
    public function test_application_loads_successfully()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
    }

    /**
     * Test that the application has proper error handling.
     */
    public function test_404_page_returns_proper_response()
    {
        $response = $this->get('/non-existent-page');

        $response->assertStatus(404);
    }

    /**
     * Test that the application handles invalid routes gracefully.
     */
    public function test_invalid_api_routes_return_404()
    {
        $response = $this->getJson('/api/invalid-endpoint');

        $response->assertStatus(404);
    }

    /**
     * Test that the application has proper CORS headers for API requests.
     */
    public function test_api_has_proper_headers()
    {
        $response = $this->getJson('/api/resume');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }
}
