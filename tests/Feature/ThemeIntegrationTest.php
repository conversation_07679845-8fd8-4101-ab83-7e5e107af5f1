<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ThemeIntegrationTest extends TestCase
{
    /**
     * Test that the home page loads successfully with modern theme.
     */
    public function test_home_page_loads_with_modern_theme()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // Add more specific assertions based on your home page structure
    }

    /**
     * Test that theme switching works end-to-end.
     */
    public function test_theme_switching_integration()
    {
        // Get current theme
        $currentResponse = $this->getJson('/api/theme/current');
        $currentResponse->assertStatus(200);
        $currentTheme = $currentResponse->json('data.name');

        // Switch to a different theme (if available)
        $availableResponse = $this->getJson('/api/theme');
        $availableResponse->assertStatus(200);
        $availableThemes = $availableResponse->json('data');

        if (count($availableThemes) > 1) {
            $newTheme = null;
            foreach ($availableThemes as $theme) {
                if ($theme['name'] !== $currentTheme) {
                    $newTheme = $theme['name'];
                    break;
                }
            }

            if ($newTheme) {
                $switchResponse = $this->postJson('/api/theme/switch', [
                    'theme' => $newTheme
                ]);
                $switchResponse->assertStatus(200);

                // Verify theme was switched
                $verifyResponse = $this->getJson('/api/theme/current');
                $verifyResponse->assertStatus(200);
                $this->assertEquals($newTheme, $verifyResponse->json('data.name'));
            }
        }

        $this->assertTrue(true); // Test passes if we reach here
    }

    /**
     * Test that resume data is properly integrated across all endpoints.
     */
    public function test_resume_data_integration()
    {
        // Get complete resume data
        $resumeResponse = $this->getJson('/api/resume');
        $resumeResponse->assertStatus(200);
        $resumeData = $resumeResponse->json('data');

        // Verify experience data consistency
        $experienceResponse = $this->getJson('/api/resume/experience');
        $experienceResponse->assertStatus(200);
        $experienceData = $experienceResponse->json('data');

        $this->assertEquals(
            count($resumeData['experience']),
            count($experienceData),
            'Experience data count mismatch between endpoints'
        );

        // Verify education data consistency
        $educationResponse = $this->getJson('/api/resume/education');
        $educationResponse->assertStatus(200);
        $educationData = $educationResponse->json('data');

        $this->assertEquals(
            count($resumeData['education']),
            count($educationData['education']),
            'Education data count mismatch between endpoints'
        );

        // Verify skills data consistency
        $skillsResponse = $this->getJson('/api/resume/skills');
        $skillsResponse->assertStatus(200);
        $skillsData = $skillsResponse->json('data');

        $this->assertEquals(
            count($resumeData['skills']),
            count($skillsData['technical_skills']),
            'Skills data count mismatch between endpoints'
        );
    }

    /**
     * Test that individual experience endpoints return consistent data.
     */
    public function test_individual_experience_endpoints_consistency()
    {
        // Get all experiences
        $allExperiencesResponse = $this->getJson('/api/resume/experience');
        $allExperiencesResponse->assertStatus(200);
        $allExperiences = $allExperiencesResponse->json('data');

        // Test each individual experience endpoint
        foreach ($allExperiences as $index => $experience) {
            $individualResponse = $this->getJson("/api/resume/experience/{$index}");
            $individualResponse->assertStatus(200);
            $individualData = $individualResponse->json('data');

            // Compare key fields
            $this->assertEquals($experience['title'], $individualData['title']);
            $this->assertEquals($experience['company'], $individualData['company']);
            $this->assertEquals($experience['location'], $individualData['location']);
            $this->assertEquals($experience['description'], $individualData['description']);
        }
    }

    /**
     * Test that search functionality works across all data types.
     */
    public function test_search_functionality_integration()
    {
        // Test search for PHP (should find in experience and skills)
        $phpSearchResponse = $this->getJson('/api/resume/search?q=PHP');
        $phpSearchResponse->assertStatus(200);
        $phpResults = $phpSearchResponse->json('data');

        $this->assertArrayHasKey('experience', $phpResults);
        $this->assertArrayHasKey('education', $phpResults);
        $this->assertArrayHasKey('skills', $phpResults);

        // Should find PHP in experience or skills
        $foundInExperience = count($phpResults['experience']) > 0;
        $foundInSkills = count($phpResults['skills']) > 0;
        $this->assertTrue(
            $foundInExperience || $foundInSkills,
            'PHP should be found in either experience or skills'
        );

        // Test search for university (should find in education)
        $universitySearchResponse = $this->getJson('/api/resume/search?q=University');
        $universitySearchResponse->assertStatus(200);
        $universityResults = $universitySearchResponse->json('data');

        $this->assertGreaterThan(
            0,
            count($universityResults['education']),
            'University should be found in education data'
        );
    }

    /**
     * Test that skills by category endpoints return consistent data.
     */
    public function test_skills_by_category_consistency()
    {
        // Get all skills
        $allSkillsResponse = $this->getJson('/api/resume/skills');
        $allSkillsResponse->assertStatus(200);
        $allSkills = $allSkillsResponse->json('data.technical_skills');

        // Test each category endpoint
        foreach ($allSkills as $categoryName => $categoryData) {
            $categoryResponse = $this->getJson("/api/resume/skills/{$categoryName}");
            $categoryResponse->assertStatus(200);
            $individualCategoryData = $categoryResponse->json('data');

            // Compare category data
            $this->assertEquals($categoryData['title'], $individualCategoryData['title']);
            $this->assertEquals($categoryData['description'], $individualCategoryData['description']);
            $this->assertEquals(
                count($categoryData['skills']),
                count($individualCategoryData['skills']),
                "Skills count mismatch for category {$categoryName}"
            );
        }
    }

    /**
     * Test that theme configuration is properly loaded and accessible.
     */
    public function test_theme_configuration_integration()
    {
        // Get theme config via API
        $configResponse = $this->getJson('/api/theme/config');
        $configResponse->assertStatus(200);
        $apiConfig = $configResponse->json('data');

        // Verify that helper functions return consistent data
        $helperExperience = get_experience_data();
        $helperEducation = get_education_data();
        $helperSkills = get_skills_by_category();

        // Compare counts
        $this->assertEquals(
            count($apiConfig['experience']['items']),
            count($helperExperience),
            'Experience count mismatch between API and helper'
        );

        $this->assertEquals(
            count($apiConfig['education']['items']),
            count($helperEducation),
            'Education count mismatch between API and helper'
        );

        $this->assertEquals(
            count($apiConfig['skills']['categories']),
            count($helperSkills),
            'Skills categories count mismatch between API and helper'
        );
    }

    /**
     * Test that all API endpoints handle errors gracefully.
     */
    public function test_api_error_handling()
    {
        // Test invalid experience index
        $invalidExperienceResponse = $this->getJson('/api/resume/experience/999');
        $invalidExperienceResponse->assertStatus(404);
        $invalidExperienceResponse->assertJson(['success' => false]);

        // Test invalid education index
        $invalidEducationResponse = $this->getJson('/api/resume/education/999');
        $invalidEducationResponse->assertStatus(404);
        $invalidEducationResponse->assertJson(['success' => false]);

        // Test invalid skills category
        $invalidSkillsResponse = $this->getJson('/api/resume/skills/invalid-category');
        $invalidSkillsResponse->assertStatus(404);
        $invalidSkillsResponse->assertJson(['success' => false]);

        // Test search without query
        $emptySearchResponse = $this->getJson('/api/resume/search');
        $emptySearchResponse->assertStatus(400);
        $emptySearchResponse->assertJson(['success' => false]);

        // Test invalid theme
        $invalidThemeResponse = $this->getJson('/api/theme/invalid-theme/metadata');
        $invalidThemeResponse->assertStatus(404);
        $invalidThemeResponse->assertJson(['success' => false]);
    }

    /**
     * Test that the application handles missing data gracefully.
     */
    public function test_missing_data_handling()
    {
        // This test would be more relevant if we had configurable data sources
        // For now, we'll test that empty results are handled properly

        $searchResponse = $this->getJson('/api/resume/search?q=nonexistentterm12345');
        $searchResponse->assertStatus(200);
        $results = $searchResponse->json('data');

        // Should return empty arrays, not errors
        $this->assertIsArray($results['experience']);
        $this->assertIsArray($results['education']);
        $this->assertIsArray($results['skills']);
    }

    /**
     * Test that response times are reasonable for all endpoints.
     */
    public function test_api_response_times()
    {
        $endpoints = [
            '/api/resume',
            '/api/resume/experience',
            '/api/resume/education',
            '/api/resume/skills',
            '/api/resume/summary',
            '/api/theme',
            '/api/theme/current',
            '/api/theme/config'
        ];

        foreach ($endpoints as $endpoint) {
            $startTime = microtime(true);
            $response = $this->getJson($endpoint);
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

            $response->assertStatus(200);
            $this->assertLessThan(
                1000, // 1 second
                $responseTime,
                "Endpoint {$endpoint} took too long to respond: {$responseTime}ms"
            );
        }
    }
}
