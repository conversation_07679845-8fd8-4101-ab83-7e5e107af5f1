# CV Application Test Suite

This directory contains comprehensive tests for the CV application, covering all major functionality including the theme system, resume API, and helper functions.

## Test Structure

### Feature Tests (`tests/Feature/`)

Feature tests test the application's behavior from a user's perspective, including HTTP requests and responses.

#### `ApplicationBasicTest.php`
- Basic application functionality
- Error handling (404 pages)
- API headers and CORS
- Route validation

#### `Api/ResumeApiTest.php`
- Complete Resume API endpoint testing
- Data structure validation
- Error handling for invalid requests
- Search functionality
- Individual item endpoints

#### `Api/ThemeApiTest.php`
- Theme system API endpoints
- Theme switching functionality
- Configuration retrieval
- Asset management
- Cache clearing

#### `ThemeIntegrationTest.php`
- End-to-end integration testing
- Data consistency across endpoints
- Performance testing
- Error handling integration
- Missing data handling

### Unit Tests (`tests/Unit/`)

Unit tests focus on testing individual components and functions in isolation.

#### `ApplicationUnitTest.php`
- Basic PHP functionality
- Laravel helper availability
- Custom helper function loading
- Configuration file access

#### `HelperFunctionsTest.php`
- All custom helper functions
- Data structure validation
- Date formatting functions
- Theme configuration access
- Expected data content validation

#### `ThemeSystemTest.php`
- Theme service initialization
- Configuration structure validation
- Data type validation
- Value reasonableness checks
- Section structure testing

#### `ResumeControllerTest.php`
- Controller method testing
- JSON response validation
- Error handling
- Data formatting
- Search functionality

## Running Tests

### Run All Tests
```bash
php artisan test
```

### Run Specific Test Suites
```bash
# Run only Feature tests
php artisan test --testsuite=Feature

# Run only Unit tests
php artisan test --testsuite=Unit
```

### Run Specific Test Files
```bash
# Run Resume API tests
php artisan test tests/Feature/Api/ResumeApiTest.php

# Run Helper function tests
php artisan test tests/Unit/HelperFunctionsTest.php
```

### Run Specific Test Methods
```bash
# Run a specific test method
php artisan test --filter test_resume_api_index_returns_successful_response
```

### Run Tests with Coverage
```bash
php artisan test --coverage
```

## Test Categories

### 🔌 **API Tests**
- **Resume API**: All endpoints for experience, education, skills, and search
- **Theme API**: Theme switching, configuration, and metadata
- **Error Handling**: Invalid requests, missing data, malformed inputs

### 🧩 **Integration Tests**
- **Data Consistency**: Ensuring data matches across different endpoints
- **Theme Integration**: Theme switching and configuration loading
- **Performance**: Response time validation
- **End-to-End**: Complete user workflows

### ⚙️ **Unit Tests**
- **Helper Functions**: All custom helper functions
- **Controllers**: Individual controller methods
- **Configuration**: Theme and application configuration
- **Data Validation**: Structure and content validation

### 🎯 **Functional Tests**
- **Search Functionality**: Text search across all data types
- **Data Formatting**: Date formatting and duration calculations
- **Icon Handling**: Skill icon path resolution
- **Error Responses**: Proper error message formatting

## Test Data

The tests use the actual configuration data from:
- `config/themes/modern.php` - Theme configuration
- Helper functions that access this configuration
- Real API endpoints and controllers

### Expected Data Structure

**Experience Data**: 4 work positions
- Content Fleet (Current)
- IMTILAK Group
- Imtilak Real Estate  
- Binoplus

**Education Data**: 2 education records
- Sakarya University (Bachelor's)
- Istiklal High School

**Skills Categories**: 4 categories
- Backend (PHP, Laravel, C#, .NET)
- Frontend (JavaScript, Vue.js, jQuery, HTML5, CSS3)
- Database (MySQL, PostgreSQL, SQL Server, SQLite)
- Tools (PhpStorm, Git, GitHub, GitLab, Bitbucket)

**Certifications**: 5+ professional certifications

## Assertions and Validations

### API Response Structure
- ✅ HTTP status codes (200, 404, 400)
- ✅ JSON structure validation
- ✅ Required fields presence
- ✅ Data type validation
- ✅ Success/error response format

### Data Content Validation
- ✅ Expected companies in experience
- ✅ Expected institutions in education
- ✅ Expected technologies in skills
- ✅ Reasonable numeric values
- ✅ Proper date formatting

### Error Handling
- ✅ Invalid indices return 404
- ✅ Missing parameters return 400
- ✅ Malformed requests handled gracefully
- ✅ Empty search results return empty arrays

## Performance Benchmarks

- ✅ API responses under 1 second
- ✅ Database queries optimized
- ✅ Memory usage reasonable
- ✅ No N+1 query problems

## Continuous Integration

These tests are designed to run in CI/CD pipelines and provide:
- ✅ Comprehensive coverage of all features
- ✅ Fast execution times
- ✅ Clear error messages
- ✅ Reliable and repeatable results

## Adding New Tests

When adding new features:

1. **Add Feature Tests** for new API endpoints
2. **Add Unit Tests** for new helper functions
3. **Update Integration Tests** for new data structures
4. **Add Performance Tests** for new heavy operations

### Test Naming Convention
- Use descriptive method names: `test_method_does_specific_thing`
- Group related tests in the same class
- Use proper namespacing for organization

### Test Documentation
- Add docblocks explaining what each test validates
- Include expected outcomes and edge cases
- Document any special setup or teardown requirements
