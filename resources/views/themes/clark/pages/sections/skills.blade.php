<section class="ftco-section" id="skills-section">
    <div class="container">
        <div class="row justify-content-center pb-5">
            <div class="col-md-12 heading-section text-center ftco-animate">
                <h1 class="big big-2">Skills</h1>
                <h2 class="mb-4">My Skills</h2>
                <p>As a web developer, I excel in HTML, CSS, JavaScript, and PHP, <br> creating dynamic and responsive websites with a keen eye for design and functionality.</p>
            </div>
        </div>
        <div class="row">

            <div class="col-xl-6 col-lg-6 col-md-6">
                <div class="row first-row">
                    @foreach(get_skills() as $skillKey=>$skill)
                        <div class="col-lg-3 col-md-4 col-6" data-toggle="tooltip" title="{{$skill}}">
                            <div class="card skill-card mb-4">
                                @if(\Illuminate\Support\Facades\File::exists(public_path("images/logos/{$skillKey}.svg")))
                                    <img src="{{asset("images/logos/{$skillKey}.svg")}}" class="single-brand"
                                         alt="{{$skill}}">
                                @else
                                    <div class="single-brand">
                                        <span>{{$skill}}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-6 d-none d-xl-block d-lg-block d-sm-block d-md-block">
                <div class="experience-area">
                    <div class="bg-panel"></div>
{{--                    <div class="d-flex flex-row years-area">--}}
{{--                        <h4 class="px-3 years">--}}
{{--                            <span class="my-1">{{ \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')) }} </span>--}}
{{--                            <span class="my-2">{{ \Carbon\Carbon::now()->diffInMonths(\Carbon\Carbon::parse('2017-05-28')) - ( \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28'))*12) }} </span>--}}
{{--                        </h4>--}}
                        <h4 class="px-4 h2 text-primary">
                            <span class=""><b>{{theme_config('about.years')}} Years</b></span>
{{--                            <span class=""><b>{{theme_config('about.months')}} Months</b></span>--}}
                            <span class=""><b>Of Experience Working</b></span>
                        </h4>
                    </div>
                    <div class="d-flex flex-row flex-wrap my-5">
                        <span><i class="fas fa-phone-alt fa-3x px-3"></i></span>
                        <div class="call-now">
                            <div class="text-uppercase h3 font-roboto"><b>Get In Touch</b></div>
                            <span class="font-roboto">
                                {!! phoneLink() !!}
                            </span>
                        </div>
                    </div>
                    <div class="d-flex flex-row flex-wrap call-area py-3">
                        <span><i class="fab fa-whatsapp fa-3x px-3"></i></span>
                        <div class="call-now">
                            <div class="text-uppercase h3 font-roboto"><b>Whatsapp</b></div>
                            <span class="font-roboto">
                                {!! whatsLink() !!}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

