<?php
$certificates = file_get_contents(storage_path("data/json/certificates.json"));
$certificates = json_decode($certificates) ?? [];
$certificateGroups = collect($certificates)->groupBy('src');
?>


<section id="certificates" class="mb-5 cc-certificate">
{{--    <h2 class="text-center mb-4 title-text">Certificates</h2>--}}
<!-- Slider main container -->
    <div class="container">
        <div class="project-title pb-5">
            <h2 class="text-uppercase title-h1">Certificates</h2>
            {{--            <h2 class="text-uppercase title-h1">Quality Work</h2>--}}
        </div>

        <div class="button-group">
            {{--            <button type="button" id="cc-certificate-btn" data-filter="*">All</button>--}}
            @foreach($certificateGroups as $group=>$certificates)
                <button type="button" class="{{$group == 'udemy' ? 'active':''}}"
                        data-filter=".{{$group}}">{{$group}}</button>
            @endforeach
        </div>

        <div class="row grid">
            @foreach($certificateGroups as $group=>$certificates)
                <div class="swiper {{$group."-swiper"}} {{$group}}">
                    <h3 class="mt-3 text-center text-capitalize">{{$group}} Certificates</h3>
                    <div class="swiper-wrapper">
                        @foreach($certificates as $key => $certificate)
                            <div class="swiper-slide">
                                <img src="{{asset($certificate->img)}}" alt="{{$certificate->title}}"/>
                            </div>
                        @endforeach
                    </div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-pagination"></div>
                </div>
            @endforeach
        </div>
    </div>
</section>

@section("custom-js")
    <script>
        $(document).ready(function () {
            var swiperTypes = [
                'cards',
                'fade',
                'coverflow',
                'cube',
                'flip',
            ];
            var swiperListCount = 0;
            @foreach($certificateGroups as $group=>$certificates)
            new Swiper(".{{$group}}-swiper", {
                // effect: swiperTypes[swiperListCount],
                effect: 'coverflow',
                grabCursor: true,
                slidesPerView: "auto",
                spaceBetween: 30,
                centeredSlides: true,
                keyboard: {
                    enabled: true,
                },
                coverflowEffect: {
                    rotate: 50,
                    stretch: 0,
                    depth: 100,
                    modifier: 1,
                    slideShadows: true,
                },
                cubeEffect: {
                    shadow: true,
                    slideShadows: true,
                    shadowOffset: 20,
                    shadowScale: 0.94,
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
            swiperListCount++;
            @endforeach
        })
    </script>

@stop