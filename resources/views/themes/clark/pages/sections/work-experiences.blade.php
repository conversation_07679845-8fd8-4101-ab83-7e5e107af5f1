<?php
$experiences = file_get_contents(storage_path("data/json/experiences.json"));
$experiences = json_decode($experiences) ?? [];
$experiences = collect($experiences)->sortByDesc('start_date')
?>
<section id="experiences" class="container cc-experience">
    <h2 class="text-center mb-4 title-text">Work Experiences</h2>

    <div class="row experiences-list">
        @foreach($experiences as $key => $experience)
            <div class="card mb-2 col-12">
                <div class="row">
                    <div class="col-md-3 bg-gradient-success aos-init aos-animate" data-aos="fade-right"
                         data-aos-offset="50"
                         data-aos-duration="500">
                        <div class="card-body cc-experience-header">
                            <p>{{\Carbon\Carbon::parse($experience->start_date)->format("M Y")}}
                                - {{ $experience->end_date? \Carbon\Carbon::parse($experience->end_date)->format("M Y"):"Present"}}</p>
                            <div class="h5">{{$experience->company}}</div>
                        </div>
                    </div>
                    <div class="col-md-9 aos-init aos-animate" data-aos="fade-left" data-aos-offset="50"
                         data-aos-duration="500">
                        <div class="card-body">
                            <div class="h5">{{$experience->title}}</div>
                            <p class="">{{$experience->description}}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</section>
