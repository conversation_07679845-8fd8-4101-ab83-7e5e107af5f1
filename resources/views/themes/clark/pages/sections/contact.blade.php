<section class="ftco-section contact-section ftco-no-pb" id="contact-section">
    <div class="container">
        <div class="row justify-content-center mb-5 pb-3">
            <div class="col-md-7 heading-section text-center ftco-animate">
                <h1 class="big big-2">Contact</h1>
                <h2 class="mb-4">Contact Me</h2>
                <p>Get in touch easily! Reach out for inquiries, collaborations, or just to say hello. Your messages are always welcome!"</p>
            </div>
        </div>

        <div class="row d-flex contact-info mb-5">
            <div class="col-6 col-xs-12 col-lg-3 d-flex ftco-animate">
                <div class="align-self-stretch box p-4 text-center">
                    <div class="icon d-flex align-items-center justify-content-center">
                        <span class="icon-map-signs"></span>
                    </div>
                    <h3 class="mb-4">Address</h3>
                    <p>{{theme_config('address')}}</p>
                </div>
            </div>
            <div class="col-6 col-xs-12 col-lg-3 d-flex ftco-animate">
                <div class="align-self-stretch box p-4 text-center">
                    <div class="icon d-flex align-items-center justify-content-center">
                        <span class="icon-phone2"></span>
                    </div>
                    <h3 class="mb-4">Contact Number</h3>
                    <p><a href="{{theme_config('phone_link')}}">{{theme_config('phone')}}</a></p>
                </div>
            </div>
            <div class="col-6 col-xs-12 col-lg-3 d-flex ftco-animate">
                <div class="align-self-stretch box p-4 text-center">
                    <div class="icon d-flex align-items-center justify-content-center">
                        <span class="icon-paper-plane"></span>
                    </div>
                    <h3 class="mb-4">Email Address</h3>
                    <p><a href="{{theme_config('email_link')}}">{{theme_config('email')}}</a></p>
                </div>
            </div>
            <div class="col-6 col-xs-12 col-lg-3 d-flex ftco-animate">
                <div class="align-self-stretch box p-4 text-center">
                    <div class="icon d-flex align-items-center justify-content-center">
                        <span class="icon-globe"></span>
                    </div>
                    <h3 class="mb-4">Website</h3>
                    <p><a href="{{theme_config('website_link')}}">{{theme_config('website')}}</a></p>
                </div>
            </div>
        </div>

        <div class="row no-gutters block-9">
            <div class="col-md-6 order-md-last d-flex">
                <form action="{{route('post_contact')}}" method="post" class="bg-light p-4 p-md-5 contact-form">
                    @csrf
                    @include('frontend.themes.clark.layout.partials.flash_message')
                    <div class="form-group">
                        <input type="text" value="{{old('name')}}" name="name" class="form-control"
                               placeholder="{{__trans('Your Name')}}">
                        @error('name') <span class="error text-danger">{{$message}}</span>@enderror
                    </div>
                    <div class="form-group">
                        <input type="text" value="{{old('email')}}" name="email" class="form-control"
                               placeholder="{{__trans('Your Email')}}">
                        @error('email') <span class="error text-danger">{{$message}}</span>@enderror
                    </div>
                    <div class="form-group">
                        <input type="text" value="{{old('subject')}}" name="subject" class="form-control"
                               placeholder="{{__trans('Subject')}}">
                        @error('subject') <span class="error text-danger">{{$message}}</span>@enderror
                    </div>
                    <div class="form-group">
                        <textarea name="message" id="" cols="30" rows="7" class="form-control"
                                  placeholder="{{__trans('Message')}}">{{old('message')}}</textarea>
                        @error('message') <span class="error text-danger">{{$message}}</span>@enderror
                    </div>
                    <div class="form-group">
                        <input type="submit" value="{{__trans('Send Message')}}" class="btn btn-primary py-3 px-5">
                    </div>
                </form>
            </div>

            <div class="col-md-6 d-flex">
                <div class="img" style="background-image: url({{asset_theme_v('images/about.jpg')}});"></div>
            </div>
        </div>
    </div>
</section>
