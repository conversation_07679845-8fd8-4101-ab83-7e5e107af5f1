<!-- ------------------------ Start skills Area ------------------------>
<?php
$educations = file_get_contents(storage_path("data/json/education.json"));
$educations = json_decode($educations) ?? [];
$educations = collect($educations)->sortByDesc('start_date');

?>


<section class="ftco-section ftco-no-pb" id="resume-section">
    <div class="container">
        <div class="row justify-content-center pb-5">
            <div class="col-md-10 heading-section text-center ftco-animate">
                <h1 class="big big-2">Resume</h1>
                <h2 class="mb-4">Resume</h2>
                <p>Explore my professional journey! Discover a comprehensive overview of my skills, experiences, and achievements. From education to employment history, delve into the details of my career path.</p>
            </div>
        </div>
        <div class="row">
            @foreach($educations as $education)
                <div class="col-md-6">
                    <div class="resume-wrap ftco-animate">
                        <span class="date">{{\Carbon\Carbon::parse($education->start_date)->year}}-{{\Carbon\Carbon::parse($education->end_date)->year}}</span>
                        <h2>{{$education->title}}</h2>
                        <p class="college">{{$education->university}} @if($education->college)
                                /
                            @endif {{$education->college}}</p>
                        <p class="mt-4">{{$education->description}}</p>
                        <div class="border-bottom rounded px-2">
                            <span class="degree">{{$education->degree}}</span>
                            <span class="address float-right"><i
                                        class="fa fa-map-marker"></i> {{$education->address}}</span>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="row justify-content-center mt-5">
            <div class="col-md-6 text-center ftco-animate">
                <p><a href="{{route('download.cv')}}" target="_blank" class="btn btn-primary py-4 px-5">Download CV</a></p>
            </div>
        </div>
    </div>
</section>

