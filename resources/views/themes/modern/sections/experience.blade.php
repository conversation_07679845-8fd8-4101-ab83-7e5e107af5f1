@php
    $experienceConfig = theme_config_get('experience', []);
    $experienceData = get_experience_data();
@endphp

<section id="experience" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ $experienceConfig['title'] ?? 'Work Experience' }}</h2>
                <p class="section-subtitle">
                    {{ $experienceConfig['subtitle'] ?? 'My Professional Journey' }}
                </p>
                @if(!empty($experienceConfig['description']))
                    <p class="text-gray-600 dark:text-gray-300 mt-4 max-w-2xl mx-auto">
                        {{ $experienceConfig['description'] }}
                    </p>
                @endif
            </div>
            
            <!-- Experience Timeline -->
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-primary-200 dark:bg-primary-800"></div>
                
                <div class="space-y-12">
                    @forelse($experienceData as $index => $experience)
                        <div class="relative flex items-start" data-animate>
                            <!-- Timeline Dot -->
                            <div class="absolute left-6 w-4 h-4 bg-primary-600 rounded-full border-4 border-white dark:border-gray-900 shadow-lg"></div>
                            
                            <!-- Experience Card -->
                            <div class="ml-16 w-full">
                                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
                                    <!-- Header -->
                                    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                        <div>
                                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ $experience['title'] }}</h3>
                                            <div class="flex flex-col md:flex-row md:items-center text-primary-600 dark:text-primary-400">
                                                @if(!empty($experience['company_url']))
                                                    <a href="{{ $experience['company_url'] }}" target="_blank" rel="nofollow" 
                                                       class="font-semibold hover:underline">
                                                        {{ $experience['company'] }}
                                                    </a>
                                                @else
                                                    <span class="font-semibold">{{ $experience['company'] }}</span>
                                                @endif
                                                @if(!empty($experience['location']))
                                                    <span class="text-gray-500 dark:text-gray-400 md:ml-2">• {{ $experience['location'] }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="mt-2 md:mt-0">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300">
                                                {{ $experience['duration'] ?? format_experience_duration($experience['start_date'], $experience['end_date'] ?? null) }}
                                            </span>
                                            @if($experience['current'] ?? false)
                                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300">
                                                    Current
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <!-- Description -->
                                    <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                        {{ $experience['description'] }}
                                    </p>
                                    
                                    <!-- Responsibilities -->
                                    @if(!empty($experience['responsibilities']))
                                        <div class="mb-6">
                                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Key Responsibilities:</h4>
                                            <ul class="space-y-2">
                                                @foreach(array_slice($experience['responsibilities'], 0, 4) as $responsibility)
                                                    <li class="flex items-start">
                                                        <svg class="w-4 h-4 text-primary-600 dark:text-primary-400 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        <span class="text-gray-600 dark:text-gray-300 text-sm">{{ $responsibility }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif
                                    
                                    <!-- Technologies -->
                                    @if(!empty($experience['technologies']) && ($experienceConfig['show_technologies'] ?? true))
                                        <div class="mb-6">
                                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Technologies Used:</h4>
                                            <div class="flex flex-wrap gap-2">
                                                @foreach($experience['technologies'] as $tech)
                                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-full">
                                                        {{ $tech }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <!-- Achievements -->
                                    @if(!empty($experience['achievements']) && ($experienceConfig['show_achievements'] ?? true))
                                        <div>
                                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Key Achievements:</h4>
                                            <ul class="space-y-2">
                                                @foreach($experience['achievements'] as $achievement)
                                                    <li class="flex items-start">
                                                        <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                        <span class="text-gray-600 dark:text-gray-300 text-sm">{{ $achievement }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-12" data-animate>
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Experience Data</h3>
                            <p class="text-gray-500 dark:text-gray-400">Experience information will be displayed here when available.</p>
                        </div>
                    @endforelse
                </div>
            </div>
            
            <!-- Summary Stats -->
            @if(count($experienceData) > 0)
                <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8" data-animate>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">{{ get_total_experience_years() }}+</div>
                        <div class="text-gray-600 dark:text-gray-300">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">{{ count($experienceData) }}</div>
                        <div class="text-gray-600 dark:text-gray-300">Companies</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">{{ theme_config_get('about.projects_completed', 26) }}+</div>
                        <div class="text-gray-600 dark:text-gray-300">Projects Completed</div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
