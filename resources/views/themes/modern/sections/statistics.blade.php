<section id="statistics" class="section bg-gradient-to-r from-primary-600 to-purple-600 text-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">Numbers That Matter</h2>
                <p class="text-xl text-primary-100 max-w-2xl mx-auto">
                    Here are some key metrics that showcase my experience and achievements
                </p>
            </div>
            
            <!-- Statistics Grid -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Statistic Item -->
                <div class="text-center" data-animate>
                    <div class="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                        </svg>
                    </div>
                    <div class="text-4xl font-bold mb-2 counter" data-target="{{ theme_config_get('about.projects_completed', 50) }}">0</div>
                    <div class="text-primary-100">Projects Completed</div>
                </div>
                
                <!-- Statistic Item -->
                <div class="text-center" data-animate>
                    <div class="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="text-4xl font-bold mb-2 counter" data-target="{{ theme_config_get('about.happy_clients', 30) }}">0</div>
                    <div class="text-primary-100">Happy Clients</div>
                </div>
                
                <!-- Statistic Item -->
                <div class="text-center" data-animate>
                    <div class="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-4xl font-bold mb-2 counter" data-target="{{ theme_config_get('about.years_experience', 5) }}">0</div>
                    <div class="text-primary-100">Years Experience</div>
                </div>
                
                <!-- Statistic Item -->
                <div class="text-center" data-animate>
                    <div class="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div class="text-4xl font-bold mb-2 counter" data-target="1000">0</div>
                    <div class="text-primary-100">Cups of Coffee</div>
                </div>
            </div>
            
            <!-- Additional Content -->
            <div class="mt-16 text-center" data-animate>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
                    <h3 class="text-2xl md:text-3xl font-bold mb-4">
                        Ready to Add Your Project to These Numbers?
                    </h3>
                    <p class="text-primary-100 mb-8 max-w-2xl mx-auto">
                        Let's work together to create something amazing and add your project to my growing list of successful collaborations.
                    </p>
                    <a href="#contact" 
                       class="inline-flex items-center px-8 py-3 bg-white text-primary-600 font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600">
                        <span>Start Your Project</span>
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Counter animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target.querySelector('.counter');
                if (counter && !counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter);
                }
            }
        });
    }, {
        threshold: 0.5
    });
    
    // Observe each counter's parent
    counters.forEach(counter => {
        observer.observe(counter.closest('[data-animate]'));
    });
});
</script>
