@php
    $resumeConfig = theme_config_get('resume', []);
    $experienceData = get_experience_data();
    $educationData = get_education_data();
@endphp

<section id="resume" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">

            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ $resumeConfig['title'] ?? 'My Resume' }}</h2>
                <p class="section-subtitle">
                    {{ $resumeConfig['subtitle'] ?? 'My professional journey and educational background' }}
                </p>
                @if (!empty($resumeConfig['description']))
                    <p class="text-gray-600 dark:text-gray-300 mt-4 max-w-2xl mx-auto">
                        {{ $resumeConfig['description'] }}
                    </p>
                @endif
            </div>

            <div class="grid md:grid-cols-2 gap-12">

                <!-- Experience -->
                <div data-animate>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 flex items-center">
                        <svg class="w-6 h-6 text-primary-600 mr-3" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6">
                            </path>
                        </svg>
                        {{ theme_config_get('experience.title', 'Experience') }}
                    </h3>

                    <div class="space-y-8">
                        @forelse($experienceData as $experience)
                            <div class="relative pl-8 border-l-2 border-primary-200 dark:border-primary-800">
                                <div class="absolute -left-2 top-0 w-4 h-4 bg-primary-600 rounded-full"></div>
                                <div
                                    class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {{ $experience['title'] }}</h4>
                                        <span class="text-sm text-primary-600 dark:text-primary-400 font-medium">
                                            {{ $experience['duration'] ?? format_experience_duration($experience['start_date'], $experience['end_date'] ?? null) }}
                                        </span>
                                    </div>
                                    <div class="flex items-center mb-3">
                                        @if (!empty($experience['company_url']))
                                            <a href="{{ $experience['company_url'] }}" target="_blank" rel="nofollow"
                                                class="text-primary-600 dark:text-primary-400 font-medium hover:underline">
                                                {{ $experience['company'] }}
                                            </a>
                                        @else
                                            <span
                                                class="text-primary-600 dark:text-primary-400 font-medium">{{ $experience['company'] }}</span>
                                        @endif
                                        @if (!empty($experience['location']))
                                            <span class="text-gray-500 dark:text-gray-400 ml-2">•
                                                {{ $experience['location'] }}</span>
                                        @endif
                                    </div>
                                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                                        {{ $experience['description'] }}
                                    </p>

                                    @if (!empty($experience['technologies']) && theme_config_get('experience.show_technologies', true))
                                        <div class="flex flex-wrap gap-2">
                                            @foreach ($experience['technologies'] as $tech)
                                                <span
                                                    class="px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-xs rounded-full">
                                                    {{ $tech }}
                                                </span>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <p class="text-gray-500 dark:text-gray-400">No experience data available.</p>
                            </div>
                        @endforelse
                    </div>
                </div>

                <!-- Education -->
                <div data-animate>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 flex items-center">
                        <svg class="w-6 h-6 text-primary-600 mr-3" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                            </path>
                        </svg>
                        {{ theme_config_get('education.title', 'Education') }}
                    </h3>

                    <div class="space-y-8">
                        @forelse($educationData as $education)
                            <div class="relative pl-8 border-l-2 border-primary-200 dark:border-primary-800">
                                <div class="absolute -left-2 top-0 w-4 h-4 bg-primary-600 rounded-full"></div>
                                <div
                                    class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {{ $education['title'] }}</h4>
                                        <span class="text-sm text-primary-600 dark:text-primary-400 font-medium">
                                            {{ $education['duration'] ?? \Carbon\Carbon::parse($education['start_date'])->format('Y') . ' - ' . \Carbon\Carbon::parse($education['end_date'])->format('Y') }}
                                        </span>
                                    </div>
                                    <div class="flex items-center mb-3">
                                        <span
                                            class="text-primary-600 dark:text-primary-400 font-medium">{{ $education['institution'] }}</span>
                                        @if (!empty($education['location']))
                                            <span class="text-gray-500 dark:text-gray-400 ml-2">•
                                                {{ $education['location'] }}</span>
                                        @endif
                                    </div>
                                    @if (!empty($education['field']))
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                            {{ $education['field'] }}</p>
                                    @endif
                                    <p class="text-gray-600 dark:text-gray-300">
                                        {{ $education['description'] }}
                                    </p>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <p class="text-gray-500 dark:text-gray-400">No education data available.</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Download Resume Button -->
            @if ($resumeConfig['show_download_button'] ?? true)
                <div class="text-center mt-12" data-animate>
                    <a href="{{ $resumeConfig['download_url'] ?? '#' }}"
                        class="inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        @if (!empty($resumeConfig['download_url'])) download @endif>
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Download Resume
                    </a>
                </div>
            @endif
        </div>
    </div>
</section>
