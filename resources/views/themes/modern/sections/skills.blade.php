@php
    $skillsConfig = theme_config_get('skills', []);
    $skillsCategories = get_skills_by_category();
    $softSkills = get_soft_skills();
@endphp

<section id="skills" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">

            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ $skillsConfig['title'] ?? 'Skills & Expertise' }}</h2>
                <p class="section-subtitle">
                    {{ $skillsConfig['subtitle'] ?? 'Technologies and tools I work with to create amazing digital experiences' }}
                </p>
                @if (!empty($skillsConfig['description']))
                    <p class="text-gray-600 dark:text-gray-300 mt-4 max-w-3xl mx-auto">
                        {{ $skillsConfig['description'] }}
                    </p>
                @endif
            </div>

            <!-- Technical Skills Categories -->
            <div class="space-y-12">
                @forelse($skillsCategories as $categoryKey => $category)
                    <div data-animate>
                        <div
                            class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700">
                            <div class="mb-6">
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                    {{ $category['title'] }}</h3>
                                @if (!empty($category['description']))
                                    <p class="text-gray-600 dark:text-gray-400">{{ $category['description'] }}</p>
                                @endif
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach ($category['skills'] as $skill)
                                    <div class="flex items-center space-x-4">
                                        @if (!empty($skill['icon']))
                                            @php $iconPath = get_skill_icon_path($skill['icon']); @endphp
                                            @if ($iconPath)
                                                <img src="{{ $iconPath }}" alt="{{ $skill['name'] }}"
                                                    class="w-8 h-8 flex-shrink-0">
                                            @else
                                                <div
                                                    class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <span
                                                        class="text-primary-600 dark:text-primary-400 text-xs font-bold">{{ substr($skill['name'], 0, 2) }}</span>
                                                </div>
                                            @endif
                                        @endif

                                        <div class="flex-1 min-w-0">
                                            <div class="flex justify-between items-center mb-2">
                                                <span
                                                    class="text-gray-700 dark:text-gray-300 font-medium">{{ $skill['name'] }}</span>
                                                @if ($skillsConfig['show_proficiency_levels'] ?? true)
                                                    <div class="flex items-center space-x-2">
                                                        @if (!empty($skill['years']))
                                                            <span
                                                                class="text-xs text-gray-500 dark:text-gray-400">{{ $skill['years'] }}y</span>
                                                        @endif
                                                        <span
                                                            class="text-primary-600 dark:text-primary-400 font-medium text-sm">{{ $skill['level'] }}%</span>
                                                    </div>
                                                @endif
                                            </div>
                                            @if ($skillsConfig['show_proficiency_levels'] ?? true)
                                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                    <div class="bg-gradient-to-r from-primary-600 to-purple-600 h-2 rounded-full transition-all duration-1000 ease-out"
                                                        style="width: {{ $skill['level'] }}%"></div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">No technical skills data available.</p>
                    </div>
                @endforelse

                <!-- Soft Skills -->
                @if ($skillsConfig['show_soft_skills'] ?? true && !empty($softSkills))
                    <div data-animate>
                        <div
                            class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700">
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Professional Skills</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach ($softSkills as $skill)
                                    <div
                                        class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                                        <div
                                            class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3">
                                            <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                        </div>
                                        <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2">
                                            {{ $skill['name'] }}</h4>
                                        @if (!empty($skill['description']))
                                            <p class="text-xs text-gray-600 dark:text-gray-400">
                                                {{ $skill['description'] }}</p>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
