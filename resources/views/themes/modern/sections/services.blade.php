<section id="services" class="section bg-gray-50 dark:bg-gray-800/50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ theme_config_get('services.title', 'My Services') }}</h2>
                <p class="section-subtitle">
                    {{ theme_config_get('services.subtitle', 'What I can do for you') }}
                </p>
            </div>
            
            <!-- Services Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach(theme_config_get('services.items', [
                    [
                        'icon' => 'code',
                        'title' => 'Web Development',
                        'description' => 'Custom web applications built with modern technologies',
                        'features' => ['Responsive Design', 'Performance Optimization', 'SEO Friendly'],
                    ],
                    [
                        'icon' => 'mobile',
                        'title' => 'Mobile Development',
                        'description' => 'Cross-platform mobile applications',
                        'features' => ['iOS & Android', 'React Native', 'Flutter'],
                    ],
                    [
                        'icon' => 'database',
                        'title' => 'Backend Development',
                        'description' => 'Robust server-side solutions and APIs',
                        'features' => ['RESTful APIs', 'Database Design', 'Cloud Integration'],
                    ],
                ]) as $service)
                    <div class="group" data-animate>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            
                            <!-- Icon -->
                            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-purple-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                @if($service['icon'] === 'code')
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                    </svg>
                                @elseif($service['icon'] === 'mobile')
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                                    </svg>
                                @elseif($service['icon'] === 'database')
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                                    </svg>
                                @else
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                @endif
                            </div>
                            
                            <!-- Content -->
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                                {{ $service['title'] }}
                            </h3>
                            
                            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                {{ $service['description'] }}
                            </p>
                            
                            <!-- Features -->
                            @if(isset($service['features']) && is_array($service['features']))
                                <ul class="space-y-2">
                                    @foreach($service['features'] as $feature)
                                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $feature }}
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- CTA Section -->
            <div class="text-center mt-16" data-animate>
                <div class="bg-gradient-to-r from-primary-600 to-purple-600 rounded-2xl p-8 md:p-12">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">
                        Ready to Start Your Project?
                    </h3>
                    <p class="text-primary-100 mb-8 max-w-2xl mx-auto">
                        Let's discuss your ideas and bring them to life with cutting-edge technology and creative solutions.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="#contact" 
                           class="inline-flex items-center px-8 py-3 bg-white text-primary-600 font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600">
                            <span>Get Started</span>
                            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                        <a href="#portfolio" 
                           class="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600">
                            View My Work
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
