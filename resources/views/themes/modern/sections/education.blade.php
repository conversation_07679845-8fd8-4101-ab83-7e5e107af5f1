@php
    $educationConfig = theme_config_get('education', []);
    $educationData = get_education_data();
    $certificationsData = get_certifications_data();
@endphp

<section id="education" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ $educationConfig['title'] ?? 'Education & Certifications' }}</h2>
                <p class="section-subtitle">
                    {{ $educationConfig['subtitle'] ?? 'Academic Background & Professional Development' }}
                </p>
                @if(!empty($educationConfig['description']))
                    <p class="text-gray-600 dark:text-gray-300 mt-4 max-w-2xl mx-auto">
                        {{ $educationConfig['description'] }}
                    </p>
                @endif
            </div>
            
            <!-- Education Timeline -->
            @if(!empty($educationData))
                <div class="mb-16">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center" data-animate>Academic Education</h3>
                    
                    <div class="relative">
                        <!-- Timeline Line -->
                        <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-primary-200 dark:bg-primary-800"></div>
                        
                        <div class="space-y-12">
                            @foreach($educationData as $education)
                                <div class="relative flex items-start" data-animate>
                                    <!-- Timeline Dot -->
                                    <div class="absolute left-6 w-4 h-4 bg-primary-600 rounded-full border-4 border-white dark:border-gray-900 shadow-lg"></div>
                                    
                                    <!-- Education Card -->
                                    <div class="ml-16 w-full">
                                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
                                            <!-- Header -->
                                            <div class="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                                                <div class="flex-1">
                                                    <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ $education['title'] }}</h4>
                                                    <div class="text-primary-600 dark:text-primary-400 font-semibold mb-1">{{ $education['institution'] }}</div>
                                                    @if(!empty($education['field']))
                                                        <div class="text-gray-600 dark:text-gray-300 mb-2">{{ $education['field'] }}</div>
                                                    @endif
                                                    @if(!empty($education['location']))
                                                        <div class="text-gray-500 dark:text-gray-400 text-sm">{{ $education['location'] }}</div>
                                                    @endif
                                                </div>
                                                <div class="mt-4 md:mt-0">
                                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300">
                                                        {{ $education['duration'] ?? \Carbon\Carbon::parse($education['start_date'])->format('Y') . ' - ' . \Carbon\Carbon::parse($education['end_date'])->format('Y') }}
                                                    </span>
                                                    @if(!empty($education['gpa']))
                                                        <div class="mt-2 text-sm text-gray-600 dark:text-gray-300">{{ $education['gpa'] }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                            
                                            <!-- Description -->
                                            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                                {{ $education['description'] }}
                                            </p>
                                            
                                            <!-- Coursework -->
                                            @if(!empty($education['coursework']) && ($educationConfig['show_coursework'] ?? true))
                                                <div class="mb-6">
                                                    <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Key Coursework:</h5>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                        @foreach(array_slice($education['coursework'], 0, 8) as $course)
                                                            <div class="flex items-center">
                                                                <svg class="w-3 h-3 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                </svg>
                                                                <span class="text-gray-600 dark:text-gray-300 text-sm">{{ $course }}</span>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                            
                                            <!-- Projects -->
                                            @if(!empty($education['projects']) && ($educationConfig['show_projects'] ?? true))
                                                <div class="mb-6">
                                                    <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Notable Projects:</h5>
                                                    <ul class="space-y-2">
                                                        @foreach($education['projects'] as $project)
                                                            <li class="flex items-start">
                                                                <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                                </svg>
                                                                <span class="text-gray-600 dark:text-gray-300 text-sm">{{ $project }}</span>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif
                                            
                                            <!-- Achievements -->
                                            @if(!empty($education['achievements']))
                                                <div>
                                                    <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Achievements:</h5>
                                                    <ul class="space-y-2">
                                                        @foreach($education['achievements'] as $achievement)
                                                            <li class="flex items-start">
                                                                <svg class="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                                </svg>
                                                                <span class="text-gray-600 dark:text-gray-300 text-sm">{{ $achievement }}</span>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
            
            <!-- Certifications -->
            @if(!empty($certificationsData) && ($educationConfig['show_certifications'] ?? true))
                <div data-animate>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Professional Certifications</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($certificationsData as $certification)
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ $certification['title'] }}</h4>
                                        <div class="text-primary-600 dark:text-primary-400 font-medium">{{ $certification['provider'] }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ $certification['date'] }}</div>
                                        @if(!empty($certification['url']))
                                            <a href="{{ $certification['url'] }}" target="_blank" rel="nofollow" 
                                               class="text-primary-600 dark:text-primary-400 hover:underline text-sm">
                                                View Certificate
                                            </a>
                                        @endif
                                    </div>
                                </div>
                                @if(!empty($certification['description']))
                                    <p class="text-gray-600 dark:text-gray-300 text-sm">{{ $certification['description'] }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
            
            <!-- Empty State -->
            @if(empty($educationData) && empty($certificationsData))
                <div class="text-center py-12" data-animate>
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Education Data</h3>
                    <p class="text-gray-500 dark:text-gray-400">Education and certification information will be displayed here when available.</p>
                </div>
            @endif
        </div>
    </div>
</section>
