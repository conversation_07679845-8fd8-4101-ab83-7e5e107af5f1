<section id="portfolio" class="section bg-gray-50 dark:bg-gray-800/50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ theme_config_get('portfolio.title', 'My Portfolio') }}</h2>
                <p class="section-subtitle">
                    {{ theme_config_get('portfolio.subtitle', 'Recent projects I\'ve worked on') }}
                </p>
            </div>
            
            <!-- Portfolio Filters -->
            @if(theme_config_get('portfolio.show_filters', true))
                <div class="flex flex-wrap justify-center gap-4 mb-12" data-animate>
                    @foreach(theme_config_get('portfolio.categories', [
                        'all' => 'All Projects',
                        'web' => 'Web Development',
                        'mobile' => 'Mobile Apps',
                        'design' => 'UI/UX Design',
                    ]) as $key => $category)
                        <button class="portfolio-filter px-6 py-2 rounded-full font-medium transition-all duration-200 {{ $key === 'all' ? 'bg-primary-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-primary-100 dark:hover:bg-gray-700' }}" 
                                data-filter="{{ $key }}">
                            {{ $category }}
                        </button>
                    @endforeach
                </div>
            @endif
            
            <!-- Portfolio Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8" id="portfolio-grid">
                @php
                    $projects = [
                        [
                            'title' => 'E-Commerce Platform',
                            'category' => 'web',
                            'description' => 'A modern e-commerce platform built with Laravel and Vue.js',
                            'image' => 'images/portfolio/project-1.jpg',
                            'technologies' => ['Laravel', 'Vue.js', 'MySQL'],
                            'link' => '#',
                            'github' => '#'
                        ],
                        [
                            'title' => 'Mobile Banking App',
                            'category' => 'mobile',
                            'description' => 'Secure mobile banking application with biometric authentication',
                            'image' => 'images/portfolio/project-2.jpg',
                            'technologies' => ['React Native', 'Node.js', 'MongoDB'],
                            'link' => '#',
                            'github' => '#'
                        ],
                        [
                            'title' => 'Dashboard UI Design',
                            'category' => 'design',
                            'description' => 'Clean and modern dashboard interface design',
                            'image' => 'images/portfolio/project-3.jpg',
                            'technologies' => ['Figma', 'Adobe XD', 'Sketch'],
                            'link' => '#',
                            'github' => '#'
                        ],
                        [
                            'title' => 'Real Estate Website',
                            'category' => 'web',
                            'description' => 'Property listing website with advanced search features',
                            'image' => 'images/portfolio/project-4.jpg',
                            'technologies' => ['Laravel', 'Tailwind CSS', 'Alpine.js'],
                            'link' => '#',
                            'github' => '#'
                        ],
                        [
                            'title' => 'Fitness Tracking App',
                            'category' => 'mobile',
                            'description' => 'Health and fitness tracking mobile application',
                            'image' => 'images/portfolio/project-5.jpg',
                            'technologies' => ['Flutter', 'Firebase', 'Dart'],
                            'link' => '#',
                            'github' => '#'
                        ],
                        [
                            'title' => 'Brand Identity Design',
                            'category' => 'design',
                            'description' => 'Complete brand identity design for tech startup',
                            'image' => 'images/portfolio/project-6.jpg',
                            'technologies' => ['Illustrator', 'Photoshop', 'InDesign'],
                            'link' => '#',
                            'github' => '#'
                        ],
                    ];
                @endphp
                
                @foreach($projects as $project)
                    <div class="portfolio-item group" data-category="{{ $project['category'] }}" data-animate>
                        <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            
                            <!-- Project Image -->
                            <div class="relative overflow-hidden">
                                <img src="{{ theme_asset($project['image']) }}" 
                                     alt="{{ $project['title'] }}"
                                     class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500">
                                
                                <!-- Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <div class="absolute bottom-4 left-4 right-4">
                                        <div class="flex space-x-2">
                                            @if($project['link'])
                                                <a href="{{ $project['link'] }}" 
                                                   target="_blank"
                                                   class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-200"
                                                   aria-label="View Project">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                    </svg>
                                                </a>
                                            @endif
                                            
                                            @if($project['github'])
                                                <a href="{{ $project['github'] }}" 
                                                   target="_blank"
                                                   class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-200"
                                                   aria-label="View Code">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                                    </svg>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Project Content -->
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                                    {{ $project['title'] }}
                                </h3>
                                
                                <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                                    {{ $project['description'] }}
                                </p>
                                
                                <!-- Technologies -->
                                <div class="flex flex-wrap gap-2">
                                    @foreach($project['technologies'] as $tech)
                                        <span class="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 text-xs font-medium rounded-full">
                                            {{ $tech }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Load More Button -->
            <div class="text-center mt-12" data-animate>
                <button class="inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <span>Load More Projects</span>
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</section>

<script>
// Portfolio filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.portfolio-filter');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => {
                btn.classList.remove('bg-primary-600', 'text-white');
                btn.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300');
            });
            
            this.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300');
            this.classList.add('bg-primary-600', 'text-white');
            
            // Filter items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
});
</script>
