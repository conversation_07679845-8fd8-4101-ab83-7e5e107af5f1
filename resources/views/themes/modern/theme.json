{"name": "modern", "title": "<PERSON><PERSON><PERSON>", "description": "A modern, responsive portfolio theme built with Tailwind CSS and Alpine.js", "version": "1.0.0", "author": "Your Name", "homepage": "https://zaherr.com", "repository": "https://github.com/zaher<PERSON><PERSON><PERSON><PERSON>/modern-theme", "license": "MIT", "supports": ["dark-mode", "responsive", "accessibility", "lazy-loading", "animations", "pwa"], "features": {"responsive": true, "dark_mode": true, "accessibility": true, "lazy_loading": true, "animations": true, "seo_optimized": true, "performance_optimized": true}, "sections": ["hero", "about", "resume", "services", "skills", "portfolio", "testimonials", "contact", "blog"], "customization": {"colors": {"primary": "#3b82f6", "secondary": "#64748b", "accent": "#f59e0b", "background": "#ffffff", "surface": "#f8fafc", "text": "#1e293b"}, "typography": {"font_family": "Inter", "heading_font": "Inter", "body_font": "Inter"}, "layout": {"container_width": "1200px", "sidebar_width": "300px", "header_height": "80px"}}, "assets": {"css": ["css/app.css"], "js": ["js/app.js"], "images": ["images/hero-bg.jpg", "images/about-bg.jpg"]}, "dependencies": {"tailwindcss": "^3.3.0", "alpinejs": "^3.13.0", "swiper": "^11.0.0", "lazysizes": "^5.3.0"}}