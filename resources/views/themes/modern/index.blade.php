@extends('themes.modern.layout')

@section('title', $page->seo_title ?? '<PERSON><PERSON><PERSON> - Web Developer')
@section('description', $page->seo_description ?? 'A modern, responsive portfolio showcasing web development skills and
    projects.')
@section('keywords', $page->seo_keywords ?? 'web developer, portfolio, modern design, responsive, full-stack')

@section('content')
    <!-- Hero Section -->
    @include('themes.modern.sections.hero')

    <!-- About Section -->
    @include('themes.modern.sections.about')

    <!-- Resume Section -->
    @include('themes.modern.sections.resume')

    <!-- Services Section -->
    @include('themes.modern.sections.services')

    <!-- Skills Section -->
    @include('themes.modern.sections.skills')

    <!-- Portfolio Section -->
    @include('themes.modern.sections.portfolio')

    <!-- Statistics Section -->
    @include('themes.modern.sections.statistics')

    <!-- Testimonials Section -->
    @include('themes.modern.sections.testimonials')

    <!-- Contact Section -->
    @include('themes.modern.sections.contact')
@endsection

@push('scripts')
    <script>
        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS (Animate On Scroll)
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100
                });
            }

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const headerHeight = document.querySelector('header').offsetHeight;
                        const targetPosition = target.offsetTop - headerHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Intersection Observer for navigation highlighting
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const id = entry.target.getAttribute('id');

                        // Remove active class from all nav links
                        navLinks.forEach(link => {
                            link.classList.remove('text-primary-600',
                                'dark:text-primary-400');
                            link.classList.add('text-gray-700', 'dark:text-gray-300');
                        });

                        // Add active class to current nav link
                        const activeLink = document.querySelector(`.nav-link[href="#${id}"]`);
                        if (activeLink) {
                            activeLink.classList.remove('text-gray-700', 'dark:text-gray-300');
                            activeLink.classList.add('text-primary-600', 'dark:text-primary-400');
                        }
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '-80px 0px -80px 0px'
            });

            sections.forEach(section => {
                observer.observe(section);
            });

            // Hide loading screen
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, 1000);
        });

        // Alpine.js components
        function navbar() {
            return {
                scrolled: false,
                mobileMenuOpen: false,
                activeSection: 'home',

                init() {
                    window.addEventListener('scroll', () => {
                        this.scrolled = window.scrollY > 50;
                    });
                },

                setActiveSection(section) {
                    this.activeSection = section;
                }
            }
        }

        function backToTop() {
            return {
                show: false,

                init() {
                    window.addEventListener('scroll', () => {
                        this.show = window.scrollY > 300;
                    });
                },

                scrollToTop() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                }
            }
        }

        function themeManager() {
            return {
                init() {
                    // Initialize theme based on localStorage or system preference
                    const savedTheme = localStorage.getItem('theme');
                    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (savedTheme) {
                        this.setTheme(savedTheme);
                    } else if (systemPrefersDark) {
                        this.setTheme('dark');
                    }

                    // Listen for system theme changes
                    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                        if (!localStorage.getItem('theme')) {
                            this.setTheme(e.matches ? 'dark' : 'light');
                        }
                    });
                },

                setTheme(theme) {
                    if (theme === 'dark') {
                        document.documentElement.classList.add('dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                    }
                }
            }
        }

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    </script>
@endpush
