<div x-data="themeToggle()" class="relative">
    <button @click="toggle()" 
            class="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :aria-label="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
            :title="isDark ? 'Switch to light mode' : 'Switch to dark mode'">
        
        <!-- Sun Icon (Light Mode) -->
        <svg x-show="!isDark" 
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="opacity-0 rotate-90 scale-75"
             x-transition:enter-end="opacity-100 rotate-0 scale-100"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="opacity-100 rotate-0 scale-100"
             x-transition:leave-end="opacity-0 -rotate-90 scale-75"
             class="w-5 h-5 text-yellow-500" 
             fill="currentColor" 
             viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
        </svg>
        
        <!-- Moon Icon (Dark Mode) -->
        <svg x-show="isDark" 
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="opacity-0 rotate-90 scale-75"
             x-transition:enter-end="opacity-100 rotate-0 scale-100"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="opacity-100 rotate-0 scale-100"
             x-transition:leave-end="opacity-0 -rotate-90 scale-75"
             class="w-5 h-5 text-blue-400" 
             fill="currentColor" 
             viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
        </svg>
    </button>
    
    <!-- Tooltip -->
    <div x-show="showTooltip" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 dark:bg-gray-700 rounded whitespace-nowrap z-50">
        <span x-text="isDark ? 'Switch to light mode' : 'Switch to dark mode'"></span>
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
    </div>
</div>

<script>
function themeToggle() {
    return {
        isDark: window.isDarkMode || false,
        showTooltip: false,
        
        init() {
            // Watch for system theme changes
            if (window.matchMedia) {
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        this.setTheme(e.matches ? 'dark' : 'light');
                    }
                });
            }
        },
        
        toggle() {
            this.isDark = !this.isDark;
            this.setTheme(this.isDark ? 'dark' : 'light');
            
            // Send request to server to update session
            fetch('/api/theme/toggle-dark-mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            }).catch(console.error);
        },
        
        setTheme(theme) {
            const html = document.documentElement;
            
            if (theme === 'dark') {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
                this.isDark = true;
            } else {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
                this.isDark = false;
            }
            
            // Dispatch custom event for other components
            window.dispatchEvent(new CustomEvent('theme-changed', { 
                detail: { theme, isDark: this.isDark } 
            }));
        }
    }
}
</script>
