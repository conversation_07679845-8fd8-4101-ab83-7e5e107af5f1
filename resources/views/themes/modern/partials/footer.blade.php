<footer class="bg-gray-900 dark:bg-gray-950 text-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Main Footer Content -->
        <div class="py-12 lg:py-16">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Brand Section -->
                <div class="lg:col-span-2">
                    <div class="mb-6">
                        <a href="{{ url('/') }}" 
                           class="flex items-center space-x-2 text-2xl font-bold hover:text-primary-400 transition-colors duration-200">
                            @if(theme_config_get('logo'))
                                <img src="{{ theme_asset(theme_config_get('logo')) }}" 
                                     alt="{{ config('app.name') }}" 
                                     class="h-8 w-auto">
                            @else
                                <span class="bg-gradient-to-r from-primary-400 to-purple-400 bg-clip-text text-transparent">
                                    {{ config('app.name', 'Portfolio') }}
                                </span>
                            @endif
                        </a>
                    </div>
                    
                    <p class="text-gray-300 mb-6 max-w-md">
                        {{ theme_config_get('hero.description', 'Creating beautiful, responsive websites and web applications using modern technologies.') }}
                    </p>
                    
                    <!-- Social Links -->
                    <div class="flex space-x-4">
                        @if(get_setting('github'))
                            <a href="{{ get_setting('github') }}" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                               aria-label="GitHub">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(get_setting('linkedin'))
                            <a href="{{ get_setting('linkedin') }}" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                               aria-label="LinkedIn">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(get_setting('twitter'))
                            <a href="{{ get_setting('twitter') }}" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                               aria-label="Twitter">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(get_setting('instagram'))
                            <a href="{{ get_setting('instagram') }}" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                               aria-label="Instagram">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.529l1.703-1.703c.389.389.925.629 1.502.629.577 0 1.113-.24 1.502-.629.389-.389.629-.925.629-1.502s-.24-1.113-.629-1.502c-.389-.389-.925-.629-1.502-.629s-1.113.24-1.502.629l-1.703-1.703c.757-.933 1.908-1.529 3.205-1.529 2.347 0 4.251 1.904 4.251 4.251s-1.904 4.251-4.251 4.251zm7.138 0c-1.297 0-2.448-.596-3.205-1.529l1.703-1.703c.389.389.925.629 1.502.629.577 0 1.113-.24 1.502-.629.389-.389.629-.925.629-1.502s-.24-1.113-.629-1.502c-.389-.389-.925-.629-1.502-.629s-1.113.24-1.502.629l-1.703-1.703c.757-.933 1.908-1.529 3.205-1.529 2.347 0 4.251 1.904 4.251 4.251s-1.904 4.251-4.251 4.251z"/>
                                </svg>
                            </a>
                        @endif
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="#home" 
                               class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                Home
                            </a>
                        </li>
                        <li>
                            <a href="#about" 
                               class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                About
                            </a>
                        </li>
                        <li>
                            <a href="#services" 
                               class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                Services
                            </a>
                        </li>
                        <li>
                            <a href="#portfolio" 
                               class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                Portfolio
                            </a>
                        </li>
                        <li>
                            <a href="#contact" 
                               class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                Contact
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Get In Touch</h3>
                    <div class="space-y-3">
                        @if(theme_config_get('contact.email'))
                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <a href="mailto:{{ theme_config_get('contact.email') }}" 
                                   class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                    {{ theme_config_get('contact.email') }}
                                </a>
                            </div>
                        @endif
                        
                        @if(theme_config_get('contact.phone'))
                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <a href="tel:{{ theme_config_get('contact.phone') }}" 
                                   class="text-gray-300 hover:text-primary-400 transition-colors duration-200">
                                    {{ theme_config_get('contact.phone') }}
                                </a>
                            </div>
                        @endif
                        
                        @if(theme_config_get('contact.address'))
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-primary-400 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="text-gray-300">
                                    {{ theme_config_get('contact.address') }}
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Newsletter Subscription -->
        <div class="border-t border-gray-800 py-8">
            <div class="max-w-md mx-auto text-center">
                <h3 class="text-lg font-semibold mb-2">Stay Updated</h3>
                <p class="text-gray-300 mb-4">Subscribe to get notified about new projects and updates.</p>
                
                <form id="newsletter-form" class="flex flex-col sm:flex-row gap-3">
                    <input type="email" 
                           name="email" 
                           placeholder="Enter your email" 
                           required
                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-primary-500 focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                    <button type="submit" 
                            data-original-text="Subscribe"
                            class="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900">
                        Subscribe
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Bottom Footer -->
        <div class="border-t border-gray-800 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-gray-400 text-sm">
                    © {{ date('Y') }} {{ config('app.name', 'Portfolio') }}. All rights reserved.
                </div>
                
                <div class="flex items-center space-x-6 text-sm">
                    <a href="/privacy" 
                       class="text-gray-400 hover:text-primary-400 transition-colors duration-200">
                        Privacy Policy
                    </a>
                    <a href="/terms" 
                       class="text-gray-400 hover:text-primary-400 transition-colors duration-200">
                        Terms of Service
                    </a>
                    <div class="flex items-center space-x-2 text-gray-400">
                        <span>Made with</span>
                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <span>using Laravel & Tailwind CSS</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
