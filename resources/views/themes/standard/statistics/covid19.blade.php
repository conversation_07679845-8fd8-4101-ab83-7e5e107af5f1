@extends('frontend.themes.standard.layout')

@section('title' , $page->seo_title ??'Web Developer - Computer Engineer')
@section('description' , $page->seo_description ??$profile->bio)
@section('keywords' , $page->seo_keywords ?? str_replace(' ',',',$prfile->bio))

@section('meta')
    <meta name="description" content="{{$page->seo_description}}">
    <meta name="keywords" content="{{$page->seo_keywords}}">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@zaherkhirullah">
    <meta name="twitter:creator" content="@zaherkhirullah">
    <meta name="twitter:title" content="{{str_replace('"', "", $page->title) }}">
    <meta name="twitter:description" content="{{ str_replace('"', "", $page->seo_description) }}">
    <meta name="twitter:image" content="{{ $page->imagePath }}">
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $page->title}}"/>
    <meta property="og:url" content="{{$page->url}}">
    <meta property="og:site_name" content="{{ get_setting('site_name') }}">
    <meta property="og:image" content="{{ $page->imagePath }}">
    <meta property="og:image:alt" content="{{$page->title}}"/>
    <meta property="og:description" content="{{ str_replace('"', "", $page->seo_description) }}"/>
    <meta property="og:locale" content="en_GB"/>
    {{--    <meta property="og:locale:alternate" content="{{ $properties['regional'] }}"/>--}}
    {{--    <meta property="article:section" content="{{ $page->title }}"/>--}}
    {{--    <meta property="article:published_time" content="{{ \Carbon\Carbon::parse($page->created_at)->tz('UTC')->toAtomString() }}"/>--}}
    {{--    <meta property="article:modified_time" content="{{ \Carbon\Carbon::parse($page->updated_at)->tz('UTC')->toAtomString()}}"/>--}}
    <?php $replace = ['"', "&nbsp;"];?>
@stop
@section('schema')
    <script data-schema="NewsArticle" type="application/ld+json">{
        "@context": "http://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {"@type": "WebPage", "@id": "{{ $page->url }}" },
        "headline": "{{ $page->title }}",
        "url": "{{ $page->url }}",
        "image": { "@type": "ImageObject", "url":"{{$page->imagePath}}", "height": 360, "width": 640 },
        "dateCreated": "{{$page->created_at }}",
        "datePublished": "{{$page->created_at }}",
        "dateModified": "{{ $page->updated_at!='' ? $page->updated_at : $page->created_at }}",
        "articleBody": "{!! str_replace($replace,"",strip_tags($page->content))  !!}",
        "author": { "@type": "Person", "name": "{{ get_setting('site_name') }}" },
         "publisher": {
          "@type": "Organization",
          "name": "{{ config('app.name') }}",
          "logo": {"@type": "ImageObject", "url": "{{ url('assets/img/logo.png') }}", "width": 60, "height": 60 }
        },
        "description": "{{$page->seo_description }}"
      }



    </script>
    <script type="application/ld+json">
          {
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              "itemListElement": [
              {"@type": "ListItem", "position": 1, "name": "{{ get_setting('site_name')   }}", "item": "{{ LaravelLocalization::getLocalizedURL(null, '/') }}"},
              {"@type": "ListItem", "position": 2, "name": "{{$page->title}}", "item": "{{ $page->url }}"}]
            }



    </script>
@stop
@section('css')
    <style>
        .nav-link {
            color: var(--gradient-color) !important;
        }

        body > * {
            color: var(--gradient-color) !important;
        }

        label {
            color: wheat;
        }

        h5.text-uppercase {
            color: #159;
        }
    </style>
    <link rel="stylesheet" media="all" href="{{asset('covid-charts')}}/deps/datatables/css/jquery.dataTables.min.css"/>
    <link rel="stylesheet" media="all" href="{{asset('covid-charts')}}/deps/datatables/css/select.dataTables.min.css"/>
    <!-- Stylesheet -->
    <link rel="stylesheet" media="all" href="{{asset('covid-charts/examples/dashboard/')}}/dark.css"/>
@stop
@section('content')
    <div class="d-flex justify-content-center my-5">
        <h1 class="my-3">{{ $page->title }}</h1>
    </div>
    <div class="flexbox">
        <div id="chartdiv"></div>
        <div id="list">
            <table id="areas" class="compact hover order-column row-border">
                <thead>
                <tr>
                    <th>Country/State</th>
                    <th>Confirmed</th>
                    <th>Deaths</th>
                    <th>Recovered</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
@stop

@section('js')
    <!-- amCharts includes -->
    <script src="{{asset('covid-charts')}}/deps/amcharts4/core.js"></script>
    <script src="{{asset('covid-charts')}}/deps/amcharts4/charts.js"></script>
    <script src="{{asset('covid-charts')}}/deps/amcharts4/maps.js"></script>

    <script src="{{asset('covid-charts')}}/deps/amcharts4/themes/dark.js"></script>
    <script src="{{asset('covid-charts')}}/deps/amcharts4/themes/animated.js"></script>

    <script src="{{asset('covid-charts')}}/deps/amcharts4-geodata/worldLow.js"></script>
    <script src="{{asset('covid-charts')}}/deps/amcharts4-geodata/data/countries2.js"></script>

    <!-- DataTables includes -->
    <script src="{{asset('covid-charts')}}/deps/jquery/jquery-3.3.1.min.js"></script>
    <script src="{{asset('covid-charts')}}/deps/datatables/js/jquery.dataTables.min.js"></script>
    <script src="{{asset('covid-charts')}}/deps/datatables/js/dataTables.select.min.js"></script>

    <!-- Data  -->
    <script src="{{asset('covid-charts')}}/data/js/world_timeline.js"></script>
    <script src="{{asset('covid-charts')}}/data/js/total_timeline.js"></script>



    <!-- Main app -->
    <script src="{{asset('covid-charts/examples/dashboard/')}}/app.js"></script>
@stop
