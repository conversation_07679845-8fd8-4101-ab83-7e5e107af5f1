@extends('frontend.themes.standard.layout')

@section('title' ,$page->seo_title??'Website development and design')

@section('description' ,$page->description?? "A blog to publish useful information for developing and designing websites step by step using the latest technologies, to reach professionalism in work and speed of performance.")

@section('keywords' ,$page->seo_keywords)

@section('amp_url',$page->ampUrl)

@section('meta')
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@zaherkhirullah">
    <meta name="twitter:creator" content="@zaherkhirullah">
    <meta name="twitter:title" content="{{str_replace('"', "", $page->title) }}">
    <meta name="twitter:description" content="{{ str_replace('"', "", $page->seo_description) }}">
    <meta name="twitter:image" content="{{ $page->imagePath }}">
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $page->title}}"/>
    <meta property="og:url" content="{{$page->url}}">
    <meta property="og:site_name" content="{{ get_setting('site_name') }}">
    <meta property="og:image" content="{{ $page->imagePath }}">
    <meta property="og:image:alt" content="{{$page->title}}"/>
    <meta property="og:description" content="{{ str_replace('"', "", $page->seo_description) }}"/>
    <meta property="og:locale" content="en_GB"/>
    {{--    <meta property="og:locale:alternate" content="{{ $properties['regional'] }}"/>--}}
    {{--    <meta property="article:section" content="{{ $page->title }}"/>--}}
    {{--    <meta property="article:published_time" content="{{ \Carbon\Carbon::parse($page->created_at)->tz('UTC')->toAtomString() }}"/>--}}
    {{--    <meta property="article:modified_time" content="{{ \Carbon\Carbon::parse($page->updated_at)->tz('UTC')->toAtomString()}}"/>--}}
@endsection

@section('schema')
    <?php $replace = ['"', "&nbsp;"];?>
    <script data-schema="NewsArticle" type="application/ld+json">
  {
        "@context": "http://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {"@type": "WebPage", "@id": "{{ $page->url }}" },
        "headline": "{{ $page->title }}",
        "url": "{{ $page->url }}",
        "image": { "@type": "ImageObject", "url":"{{$page->imagePath}}", "height": 360, "width": 640 },
        "dateCreated": "{{$page->created_at }}",
        "datePublished": "{{$page->created_at }}",
        "dateModified": "{{ $page->updated_at!='' ? $page->updated_at : $page->created_at }}",
        "articleBody": "{!! str_replace($replace,"",strip_tags($page->content))  !!}",
        "author": { "@type": "Person", "name": "{{ get_setting('site_name') }}" },
         "publisher": {
          "@type": "Organization",
          "name": "{{ config('app.name') }}",
          "logo": {"@type": "ImageObject", "url": "{{ url('assets/img/logo.png') }}", "width": 60, "height": 60 }
        },
        "description": "{{$page->seo_description }}"
      }


    </script>
    <script type="application/ld+json">
  {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
      {"@type": "ListItem", "position": 1, "name": "{{ get_setting('site_name')   }}", "item": "{{ LaravelLocalization::getLocalizedURL(null, '/') }}"},
      {"@type": "ListItem", "position": 2, "name": "{{$page->title}}", "item": "{{ $page->url }}"}]
    }

    </script>
@endsection


@section('content')
    <!-- Styles -->
    {{--    <div class="flex-center position-ref full-height">--}}
    {{--        <div class="flex-center border">--}}
    {{--            <div class="code">--}}
    {{--                @yield('code',"Blog")--}}
    {{--            </div>--}}
    {{--            <div class="message" style="">--}}
    {{--                @yield('message', "Coming Soon")--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--        <div class="home">--}}
    {{--            <a href="{{url('/')}}" class="px-2">{{__('Go to home')}}</a>--}}
    {{--        </div>--}}
    {{--    </div>--}}
    <!------------------------ Start Banner Area ------------------------>
    <section id="banner" class="site-banner">
        <div class="row">
            <div class="col-lg-6 col-md-12 site-title">
                <h3 class="title-text">Blog</h3>
                <h1 class="title-text text-uppercase">Coming Soon</h1>
                <h4 class="title-text text-uppercase">Our Technology Blog is Coming Soon</h4>
                <div class="site-buttons">
                    <div class="d-flex flex-row flex-wrap">
                        <a href="https://news.zaherr.com" class="btn button primary-button text-capitalize m-2"><i class="fas fa-file-alt"></i> News</a>
                        <button type="button" class="btn button secondary-button text-capitalize m-2"><i class="far fa-newspaper"></i> Articles</button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12 banner-image">
                <img src="{{asset_theme_v('assets/img/svg/undraw_online_friends_x73e.svg')}}" alt="banner-image" class="img-fluid">
            </div>
        </div>
    </section>
    <!------------------------ End Banner Area ------------------------>
@stop