<!-- ------------------------ Start skills Area ------------------------>
<?php
$educations = file_get_contents(storage_path("data/json/education.json"));
$educations = json_decode($educations) ?? [];
$educations = collect($educations)->sortByDesc('start_date')
?>
<section id="educations" class="container cc-education my-4">
    <h2 class="text-center mb-4 title-text">Education</h2>
    <div class="mx-0 mx-lg-2">
        <div class="row educations-list">
            @foreach($educations as $key => $education)
                <div class="card mb-2 col-12">
                    <div class="row">
                        <div class="col-md-3 bg-gradient-primary aos-init aos-animate" data-aos="fade-right"
                             data-aos-offset="50" data-aos-duration="500">
                            <div class="card-body mt-3 cc-education-header">
                                <p>{{\Carbon\Carbon::parse($education->start_date)->format("Y")}}
                                    - {{ $education->end_date? \Carbon\Carbon::parse($education->end_date)->format("Y"):"Present"}}</p>
                                <div class="h5">{{$education->degree}}</div>
                            </div>
                        </div>
                        <div class="col-md-9 aos-init aos-animate" data-aos="fade-left" data-aos-offset="50"
                             data-aos-duration="500">
                            <div class="card-body">
                                <div class="h5">{{$education->title}}</div>
                                <p class="university">{{$education->university}} / {{$education->college}}</p>
{{--                                <p class="college"></p>--}}
{{--                                <p class="description">{{$education->description}}</p>--}}
                                <div class="d-flex">
                                    <span class="mr-0 ml-auto">
                                      <i class="fa fa-map-marked-alt"></i>  {{$education->address}}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
<!-- ------------------------ End skills Area ------------------------>