<!------------------------ Start Skills Area ------------------------>
<section id="brand" class="skills-area">
    <div class="text-center my-4">
        <h2 class="text-uppercase title-text">
            SKILLS
        </h2>
    </div>

    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-6">
            <div class="row first-row">
                @foreach(get_skills() as $skillKey=>$skill)
                    <div class="col-lg-3 col-md-4 col-6" data-toggle="tooltip" title="{{$skill}}">
                        <div class="card skill-card">
                            @if(\Illuminate\Support\Facades\File::exists(public_path("images/logos/{$skillKey}.svg")))
                                <img src="{{asset("images/logos/{$skillKey}.svg")}}" class="single-brand"
                                     alt="{{$skill}}">
                            @else
                                <div class="single-brand">
                                    <span>{{$skill}}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
                <div class="col-lg-3 col-md-4 col-6" data-toggle="tooltip" title="{{$skill}}">
                    <div class="card skill-card">
                        <div class="single-brand plus-card">
                            +
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 d-none d-xl-block d-lg-block d-sm-block d-md-block">
            <div class="experience-area">
                <div class="bg-panel"></div>
                <div class="d-flex flex-row years-area">
                    <h4 class="px-3 years">
                        <span class="my-1">{{ \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')) }} </span>
                        <span class="my-2">{{ \Carbon\Carbon::now()->diffInMonths(\Carbon\Carbon::parse('2017-05-28')) - ( \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28'))*12) }} </span>
                    </h4>
                    <h4 class="px-2 h3">
                        <span class="my-2"><b>Years</b></span>
                        <span class="my-3"><b>Months</b></span>
                        <span class="my-2"><b>Of Experience Working</b></span>
                    </h4>
                </div>
                <div class="d-flex flex-row flex-wrap call-area">
                    <span><i class="fas fa-phone-alt fa-3x px-3"></i></span>
                    <div class="call-now">
                        <div class="text-uppercase h3 font-roboto"><b>Get In Touch</b></div>
                        <span class="font-roboto">
                                {!! phoneLink() !!}
                            </span>
                    </div>
                </div>
                <div class="d-flex flex-row flex-wrap call-area py-3">
                    <span><i class="fab fa-whatsapp fa-3x px-3"></i></span>
                    <div class="call-now">
                        <div class="text-uppercase h3 font-roboto"><b>Whatsapp</b></div>
                        <span class="font-roboto">
                                {!! whatsLink() !!}
                            </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!------------------------ End Skills Area ------------------------>
