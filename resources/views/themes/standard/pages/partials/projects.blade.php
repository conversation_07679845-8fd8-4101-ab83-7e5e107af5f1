<!-- ------------------------ Start skills Area ------------------------>
<?php
$certificates = file_get_contents(storage_path("data/json/certificates.json"));
$certificates = json_decode($certificates) ?? [];
$certificates = collect($certificates)->sortByDesc('start_date');
$certificateGroups = collect($certificates)->groupBy('src');
?>

<!------------------------ Project Area ------------------------>
<section id="projects" class="project-area">
    <div class="container">
        <div class="project-title pb-5">
            <h2 class="text-uppercase title-h1">Recently Done Project</h2>
            <h2 class="text-uppercase title-h1">Quality Work</h2>
        </div>

        <div class="button-group">
            <button type="button" class="active" id="project-area-btn" data-filter="*">All</button>
            @foreach($certificateGroups as $group=>$certificates)
                <button type="button" data-filter=".{{$group}}">{{$group}}</button>
            @endforeach
        </div>

        <div class="row grid">

            @foreach($certificateGroups as $group=>$certificates)
                {{--                @foreach($certificates as $certificate)--}}
                {{--                    <div class="col-lg-4 col-md-6 col-sm-12 element-item {{$group}}">--}}
                {{--                        <div class="our-project">--}}
                {{--                            <div class="img">--}}
                {{--                                <a class="test-popup-link" href="{{$certificate->img}}">--}}
                {{--                                    <img src="{{$certificate->img}}" alt="{{$certificate->title}}"--}}
                {{--                                         class="img-fluid">--}}
                {{--                                </a>--}}
                {{--                            </div>--}}
                {{--                            <div class="title py-4">--}}
                {{--                                <h4 class="text-uppercase">{{$certificate->title}}</h4>--}}
                {{--                                <span class="text-secondary">{{$certificate->start_date, $certificate->end_date}}</span>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                @endforeach--}}

                <div class="swiper {{$group."-swiper"}} {{$group}}">
                    <h3 class="mt-3 text-center text-capitalize">{{$group}} Certificates</h3>
                    <div class="swiper-wrapper">
                        @foreach($certificates as $key => $certificate)
                            <div class="swiper-slide">
                                <img src="{{asset($certificate->img)}}" alt="{{$certificate->title}}"/>
                            </div>
                        @endforeach
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!------------------------ End Project Area ------------------------>
