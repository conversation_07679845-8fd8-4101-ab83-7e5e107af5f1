@extends('frontend.themes.kross.layout')

@section('title' ,$page->seo_title??'Website development and design')

@section('description' ,$page->description?? "A blog to publish useful information for developing and designing websites step by step using the latest technologies, to reach professionalism in work and speed of performance.")

@section('keywords' ,$page->seo_keywords)

@section('amp_url',$page->ampUrl)

@section('meta')
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@zaherkhirullah">
    <meta name="twitter:creator" content="@zaherkhirullah">
    <meta name="twitter:title" content="{{str_replace('"', "", $page->title) }}">
    <meta name="twitter:description" content="{{ str_replace('"', "", $page->seo_description) }}">
    <meta name="twitter:image" content="{{ $page->imagePath }}">
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $page->title}}"/>
    <meta property="og:url" content="{{$page->url}}">
    <meta property="og:site_name" content="{{ get_setting('site_name') }}">
    <meta property="og:image" content="{{ $page->imagePath }}">
    <meta property="og:image:alt" content="{{$page->title}}"/>
    <meta property="og:description" content="{{ str_replace('"', "", $page->seo_description) }}"/>
    <meta property="og:locale" content="en_GB"/>
    {{--    <meta property="og:locale:alternate" content="{{ $properties['regional'] }}"/>--}}
    {{--    <meta property="article:section" content="{{ $page->title }}"/>--}}
    {{--    <meta property="article:published_time" content="{{ \Carbon\Carbon::parse($page->created_at)->tz('UTC')->toAtomString() }}"/>--}}
    {{--    <meta property="article:modified_time" content="{{ \Carbon\Carbon::parse($page->updated_at)->tz('UTC')->toAtomString()}}"/>--}}
@endsection

@section('schema')
    <?php $replace = ['"', "&nbsp;"]; ?>
    <script data-schema="NewsArticle" type="application/ld+json">
  {
        "@context": "http://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {"@type": "WebPage", "@id": "{{ $page->url }}" },
        "headline": "{{ $page->title }}",
        "url": "{{ $page->url }}",
        "image": { "@type": "ImageObject", "url":"{{$page->imagePath}}", "height": 360, "width": 640 },
        "dateCreated": "{{$page->created_at }}",
        "datePublished": "{{$page->created_at }}",
        "dateModified": "{{ $page->updated_at!='' ? $page->updated_at : $page->created_at }}",
        "articleBody": "{!! str_replace($replace,"",strip_tags($page->content))  !!}",
        "author": { "@type": "Person", "name": "{{ get_setting('site_name') }}" },
         "publisher": {
          "@type": "Organization",
          "name": "{{ config('app.name') }}",
          "logo": {"@type": "ImageObject", "url": "{{ url('assets/img/logo.png') }}", "width": 60, "height": 60 }
        },
        "description": "{{$page->seo_description }}"
      }



    </script>
    <script type="application/ld+json">
  {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
      {"@type": "ListItem", "position": 1, "name": "{{ get_setting('site_name')   }}
        ", "item": "{{ LaravelLocalization::getLocalizedURL(null, '/') }}"},
      {"@type": "ListItem", "position": 2, "name": "{{$page->title}}", "item": "{{ $page->url }}"}]
    }


    </script>
@endsection


@section('content')

    @include('frontend.themes.kross.layout.partials.page_title',['pageTitle'=>__trans('Blogs')])

    <!-- blog -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-5.jpg')}}"
                             alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income
                                    1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
                                eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-2.jpg')}}"
                             alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income
                                    1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
                                eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-3.jpg')}}"
                             alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income
                                    1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
                                eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-4.jpg')}}"
                             alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income
                                    1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
                                eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-5.jpg')}}"
                             alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income
                                    1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
                                eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
                <div class="col-lg-4 col-sm-6 mb-4">
                    <article class="card shadow">
                        <img class="rounded card-img-top" src="{{asset_theme_v('images/blog/post-2.jpg')}}" alt="post-thumb">
                        <div class="card-body">
                            <h4 class="card-title"><a class="text-dark" href="blog-single.html">Amazon increase income 1.5 Million</a>
                            </h4>
                            <p class="cars-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor
                                incididunt ut labore et
                                dolore magna aliqua.</p>
                            <a href="blog-single.html" class="btn btn-xs btn-primary">Read More</a>
                        </div>
                    </article>
                </div>
            </div>
        </div>
    </section>
    <!-- /blog -->
@stop