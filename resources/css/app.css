@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS for Modern Theme */

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
  
  ::-moz-selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component Styles */
@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply border-2 border-gray-300 hover:border-primary-600 text-gray-700 hover:text-primary-600 focus:ring-primary-500;
  }
  
  .btn-lg {
    @apply px-8 py-3 text-lg;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  /* Card Components */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }
  
  /* Form Components */
  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary-500 focus:ring-1 focus:ring-primary-500 transition-colors duration-200;
  }
  
  .form-textarea {
    @apply form-input resize-none;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  }
  
  /* Section Components */
  .section {
    @apply py-20 lg:py-24;
  }
  
  .section-title {
    @apply text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white text-center mb-4;
  }
  
  .section-subtitle {
    @apply text-lg text-gray-600 dark:text-gray-400 text-center mb-12 max-w-2xl mx-auto;
  }
  
  /* Navigation Components */
  .nav-link {
    @apply relative px-3 py-2 text-sm font-medium transition-colors duration-200;
  }
  
  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300;
  }
  
  .nav-link:hover::after,
  .nav-link.active::after {
    @apply w-full;
  }
  
  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }
  
  .slide-up {
    animation: slideUp 0.6s ease-out;
  }
  
  .slide-down {
    animation: slideDown 0.6s ease-out;
  }
  
  .scale-in {
    animation: scaleIn 0.4s ease-out;
  }
  
  .bounce-in {
    animation: bounceIn 0.8s ease-out;
  }
  
  /* Loading Animation */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-purple-600 to-pink-600 bg-clip-text text-transparent;
  }
  
  /* Glass Effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-gray-900/10 backdrop-blur-md border border-gray-700/20;
  }
}

/* Utility Classes */
@layer utilities {
  /* Animation Delays */
  .animation-delay-100 { animation-delay: 100ms; }
  .animation-delay-200 { animation-delay: 200ms; }
  .animation-delay-300 { animation-delay: 300ms; }
  .animation-delay-500 { animation-delay: 500ms; }
  .animation-delay-700 { animation-delay: 700ms; }
  .animation-delay-1000 { animation-delay: 1000ms; }
  .animation-delay-1500 { animation-delay: 1500ms; }
  .animation-delay-2000 { animation-delay: 2000ms; }
  
  /* Text Shadows */
  .text-shadow-sm { text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); }
  .text-shadow { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
  .text-shadow-lg { text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12); }
  
  /* Box Shadows */
  .shadow-colored { box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.1); }
  .shadow-colored-lg { box-shadow: 0 20px 40px -4px rgba(59, 130, 246, 0.15); }
  
  /* Aspect Ratios */
  .aspect-square { aspect-ratio: 1 / 1; }
  .aspect-video { aspect-ratio: 16 / 9; }
  .aspect-photo { aspect-ratio: 4 / 3; }
  
  /* Custom Spacing */
  .space-y-18 > :not([hidden]) ~ :not([hidden]) { margin-top: 4.5rem; }
  .space-y-88 > :not([hidden]) ~ :not([hidden]) { margin-top: 22rem; }
  
  /* Responsive Text */
  .text-responsive {
    font-size: clamp(1rem, 4vw, 1.5rem);
  }
  
  .text-responsive-lg {
    font-size: clamp(1.5rem, 6vw, 3rem);
  }
  
  .text-responsive-xl {
    font-size: clamp(2rem, 8vw, 4rem);
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .dark-mode-image {
    filter: brightness(0.8) contrast(1.2);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
