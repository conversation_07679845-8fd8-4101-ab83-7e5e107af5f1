import Alpine from 'alpinejs'
import axios from 'axios'

// Make Alpine available globally
window.Alpine = Alpine

// Configure axios
window.axios = axios
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'

// Get CSRF token
const token = document.head.querySelector('meta[name="csrf-token"]')
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content
}

// Theme Management
class ThemeManager {
    constructor() {
        this.currentTheme = 'modern'
        this.isDarkMode = this.getStoredDarkMode()
        this.init()
    }

    init() {
        this.applyTheme()
        this.setupEventListeners()
        this.initializeComponents()
    }

    getStoredDarkMode() {
        const stored = localStorage.getItem('darkMode')
        if (stored !== null) {
            return JSON.parse(stored)
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches
    }

    applyTheme() {
        if (this.isDarkMode) {
            document.documentElement.classList.add('dark')
        } else {
            document.documentElement.classList.remove('dark')
        }
    }

    toggleDarkMode() {
        this.isDarkMode = !this.isDarkMode
        localStorage.setItem('darkMode', JSON.stringify(this.isDarkMode))
        this.applyTheme()
        
        // Notify server
        this.updateServerDarkMode()
        
        // Dispatch event
        window.dispatchEvent(new CustomEvent('darkModeToggled', {
            detail: { isDarkMode: this.isDarkMode }
        }))
    }

    async updateServerDarkMode() {
        try {
            await axios.post('/api/theme/toggle-dark-mode')
        } catch (error) {
            console.error('Failed to update dark mode on server:', error)
        }
    }

    setupEventListeners() {
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (localStorage.getItem('darkMode') === null) {
                this.isDarkMode = e.matches
                this.applyTheme()
            }
        })
    }

    initializeComponents() {
        // Initialize lazy loading
        this.initLazyLoading()
        
        // Initialize smooth scrolling
        this.initSmoothScrolling()
        
        // Initialize intersection observer for animations
        this.initScrollAnimations()
        
        // Initialize performance optimizations
        this.initPerformanceOptimizations()
    }

    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target
                        img.src = img.dataset.src
                        img.classList.remove('lazy')
                        img.classList.add('fade-in')
                        observer.unobserve(img)
                    }
                })
            })

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img)
            })
        }
    }

    initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault()
                const target = document.querySelector(this.getAttribute('href'))
                if (target) {
                    const headerHeight = document.querySelector('header')?.offsetHeight || 0
                    const targetPosition = target.offsetTop - headerHeight - 20

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    })
                }
            })
        })
    }

    initScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in')
                    }
                })
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            })

            document.querySelectorAll('[data-animate]').forEach(el => {
                animationObserver.observe(el)
            })
        }
    }

    initPerformanceOptimizations() {
        // Preload critical resources
        this.preloadCriticalResources()
        
        // Optimize images
        this.optimizeImages()
        
        // Setup service worker if available
        this.setupServiceWorker()
    }

    preloadCriticalResources() {
        const criticalResources = [
            '/themes/modern/css/app.css',
            '/themes/modern/js/app.js'
        ]

        criticalResources.forEach(resource => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.href = resource
            link.as = resource.endsWith('.css') ? 'style' : 'script'
            document.head.appendChild(link)
        })
    }

    optimizeImages() {
        // Add loading="lazy" to images below the fold
        const images = document.querySelectorAll('img:not([loading])')
        images.forEach((img, index) => {
            if (index > 2) { // Skip first 3 images (likely above the fold)
                img.loading = 'lazy'
            }
        })
    }

    async setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/sw.js')
                console.log('Service Worker registered successfully')
            } catch (error) {
                console.log('Service Worker registration failed:', error)
            }
        }
    }
}

// Navigation Manager
class NavigationManager {
    constructor() {
        this.activeSection = 'home'
        this.sections = []
        this.navLinks = []
        this.init()
    }

    init() {
        this.sections = document.querySelectorAll('section[id]')
        this.navLinks = document.querySelectorAll('.nav-link')
        this.setupIntersectionObserver()
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.setActiveSection(entry.target.id)
                }
            })
        }, {
            threshold: 0.3,
            rootMargin: '-80px 0px -80px 0px'
        })

        this.sections.forEach(section => {
            observer.observe(section)
        })
    }

    setActiveSection(sectionId) {
        this.activeSection = sectionId
        
        // Update navigation links
        this.navLinks.forEach(link => {
            link.classList.remove('text-primary-600', 'dark:text-primary-400', 'active')
            link.classList.add('text-gray-700', 'dark:text-gray-300')
        })

        const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`)
        if (activeLink) {
            activeLink.classList.remove('text-gray-700', 'dark:text-gray-300')
            activeLink.classList.add('text-primary-600', 'dark:text-primary-400', 'active')
        }
    }
}

// Form Handler
class FormHandler {
    constructor() {
        this.init()
    }

    init() {
        this.setupContactForm()
        this.setupNewsletterForm()
    }

    setupContactForm() {
        const contactForm = document.getElementById('contact-form')
        if (contactForm) {
            contactForm.addEventListener('submit', this.handleContactSubmit.bind(this))
        }
    }

    setupNewsletterForm() {
        const newsletterForm = document.getElementById('newsletter-form')
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', this.handleNewsletterSubmit.bind(this))
        }
    }

    async handleContactSubmit(e) {
        e.preventDefault()
        const form = e.target
        const formData = new FormData(form)
        const submitButton = form.querySelector('button[type="submit"]')
        
        try {
            this.setButtonLoading(submitButton, true)
            
            const response = await axios.post('/api/contact', formData)
            
            if (response.data.success) {
                this.showNotification('Message sent successfully!', 'success')
                form.reset()
            } else {
                this.showNotification('Failed to send message. Please try again.', 'error')
            }
        } catch (error) {
            this.showNotification('An error occurred. Please try again.', 'error')
            console.error('Contact form error:', error)
        } finally {
            this.setButtonLoading(submitButton, false)
        }
    }

    async handleNewsletterSubmit(e) {
        e.preventDefault()
        const form = e.target
        const formData = new FormData(form)
        const submitButton = form.querySelector('button[type="submit"]')
        
        try {
            this.setButtonLoading(submitButton, true)
            
            const response = await axios.post('/api/newsletter', formData)
            
            if (response.data.success) {
                this.showNotification('Successfully subscribed to newsletter!', 'success')
                form.reset()
            } else {
                this.showNotification('Failed to subscribe. Please try again.', 'error')
            }
        } catch (error) {
            this.showNotification('An error occurred. Please try again.', 'error')
            console.error('Newsletter form error:', error)
        } finally {
            this.setButtonLoading(submitButton, false)
        }
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true
            button.innerHTML = '<div class="loading-spinner w-5 h-5 mr-2"></div>Sending...'
        } else {
            button.disabled = false
            button.innerHTML = button.dataset.originalText || 'Send Message'
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div')
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`
        notification.textContent = message

        document.body.appendChild(notification)

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full')
        }, 100)

        // Remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full')
            setTimeout(() => {
                document.body.removeChild(notification)
            }, 300)
        }, 5000)
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize theme manager
    window.themeManager = new ThemeManager()
    
    // Initialize navigation manager
    window.navigationManager = new NavigationManager()
    
    // Initialize form handler
    window.formHandler = new FormHandler()
    
    // Start Alpine.js
    Alpine.start()
    
    // Hide loading screen
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen')
        if (loadingScreen) {
            loadingScreen.style.opacity = '0'
            setTimeout(() => {
                loadingScreen.style.display = 'none'
            }, 500)
        }
    }, 1000)
})

// Export for use in other modules
export { ThemeManager, NavigationManager, FormHandler }
