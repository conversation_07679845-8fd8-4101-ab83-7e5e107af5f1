<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Config;

class ThemeService
{
    protected array $themes = [];
    protected ?string $currentTheme = null;
    protected array $themeConfig = [];

    public function __construct()
    {
        $this->loadAvailableThemes();
        $this->setCurrentTheme();
    }

    /**
     * Load all available themes from the themes directory
     */
    protected function loadAvailableThemes(): void
    {
        $themesPath = resource_path('views/themes');

        if (!File::exists($themesPath)) {
            return;
        }

        $directories = File::directories($themesPath);

        foreach ($directories as $directory) {
            $themeName = basename($directory);
            $configPath = $directory . '/theme.json';

            if (File::exists($configPath)) {
                $config = json_decode(File::get($configPath), true);
                $this->themes[$themeName] = array_merge($config, [
                    'name' => $themeName,
                    'path' => $directory,
                ]);
            } else {
                // Default theme configuration
                $this->themes[$themeName] = [
                    'name' => $themeName,
                    'title' => ucfirst($themeName),
                    'description' => "The {$themeName} theme",
                    'version' => '1.0.0',
                    'author' => 'Unknown',
                    'path' => $directory,
                    'supports' => ['dark-mode', 'responsive'],
                ];
            }
        }
    }

    /**
     * Set the current theme
     */
    protected function setCurrentTheme(): void
    {
        // Priority: Session > Config > Default
        $theme = Session::get('theme') ??
            Config::get('app.default_theme') ??
            'modern';

        if (isset($this->themes[$theme])) {
            $this->currentTheme = $theme;
            $this->loadThemeConfig($theme);
        } else {
            // Fallback to first available theme
            $this->currentTheme = array_key_first($this->themes) ?? 'modern';
            if ($this->currentTheme) {
                $this->loadThemeConfig($this->currentTheme);
            }
        }
    }

    /**
     * Load theme-specific configuration
     */
    protected function loadThemeConfig(string $themeName): void
    {
        $configPath = config_path("themes/{$themeName}.php");

        if (File::exists($configPath)) {
            $this->themeConfig = include $configPath;
        } else {
            $this->themeConfig = [];
        }
    }

    /**
     * Get the current theme
     */
    public function getCurrentTheme(): ?array
    {
        return $this->currentTheme ? $this->themes[$this->currentTheme] : null;
    }

    /**
     * Get current theme name
     */
    public function getCurrentThemeName(): ?string
    {
        return $this->currentTheme;
    }

    /**
     * Get all available themes
     */
    public function getAvailableThemes(): array
    {
        return $this->themes;
    }

    /**
     * Switch to a different theme
     */
    public function switchTheme(string $themeName): bool
    {
        if (!isset($this->themes[$themeName])) {
            return false;
        }

        $this->currentTheme = $themeName;
        $this->loadThemeConfig($themeName);
        Session::put('theme', $themeName);

        return true;
    }

    /**
     * Get theme configuration
     */
    public function getThemeConfig(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->themeConfig;
        }

        return data_get($this->themeConfig, $key, $default);
    }

    /**
     * Check if current theme supports a feature
     */
    public function supports(string $feature): bool
    {
        $currentTheme = $this->getCurrentTheme();

        if (!$currentTheme) {
            return false;
        }

        return in_array($feature, $currentTheme['supports'] ?? []);
    }

    /**
     * Check if dark mode is enabled
     */
    public function isDarkMode(): bool
    {
        return Session::get('dark_mode', false) && $this->supports('dark-mode');
    }

    /**
     * Toggle dark mode
     */
    public function toggleDarkMode(): bool
    {
        if (!$this->supports('dark-mode')) {
            return false;
        }

        $darkMode = !$this->isDarkMode();
        Session::put('dark_mode', $darkMode);

        return $darkMode;
    }

    /**
     * Get theme asset URL
     */
    public function asset(string $path): string
    {
        $themeName = $this->getCurrentThemeName();

        if (!$themeName) {
            return asset($path);
        }

        $fullPath = "themes/{$themeName}/{$path}";

        return asset($fullPath) . '?v=' . $this->getAssetVersion($fullPath);
    }

    /**
     * Get asset version for cache busting
     */
    protected function getAssetVersion(string $path): string
    {
        $fullPath = public_path($path);

        if (File::exists($fullPath)) {
            return substr(md5_file($fullPath), 0, 8);
        }

        return time();
    }

    /**
     * Get theme view path
     */
    public function getViewPath(string $view): string
    {
        $themeName = $this->getCurrentThemeName();

        if (!$themeName) {
            return $view;
        }

        return "themes.{$themeName}.{$view}";
    }

    /**
     * Check if a theme exists
     */
    public function exists(string $themeName): bool
    {
        return isset($this->themes[$themeName]);
    }

    /**
     * Get theme metadata
     */
    public function getThemeMetadata(string $themeName): ?array
    {
        return $this->themes[$themeName] ?? null;
    }

    /**
     * Cache theme data for performance
     */
    public function cacheThemeData(): void
    {
        Cache::put('themes.available', $this->themes, now()->addHours(24));
        Cache::put('themes.current', $this->currentTheme, now()->addHours(24));
    }

    /**
     * Clear theme cache
     */
    public function clearCache(): void
    {
        Cache::forget('themes.available');
        Cache::forget('themes.current');
    }
}
