<?php

namespace App\Providers;

use App\Services\ThemeService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Blade;

class ThemeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ThemeService::class, function ($app) {
            return new ThemeService();
        });

        $this->app->alias(ThemeService::class, 'theme');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register theme view paths
        $this->registerThemeViewPaths();
        
        // Register theme blade directives
        $this->registerBladeDirectives();
        
        // Register theme view composers
        $this->registerViewComposers();
    }

    /**
     * Register theme view paths
     */
    protected function registerThemeViewPaths(): void
    {
        $themeService = $this->app->make(ThemeService::class);
        $currentTheme = $themeService->getCurrentTheme();
        
        if ($currentTheme) {
            $themePath = resource_path("views/themes/{$currentTheme['name']}");
            if (is_dir($themePath)) {
                View::addLocation($themePath);
            }
        }
    }

    /**
     * Register custom blade directives for themes
     */
    protected function registerBladeDirectives(): void
    {
        // @theme directive for theme-specific content
        Blade::directive('theme', function ($expression) {
            return "<?php if(theme_is({$expression})): ?>";
        });

        Blade::directive('endtheme', function () {
            return "<?php endif; ?>";
        });

        // @themeAsset directive for theme assets
        Blade::directive('themeAsset', function ($expression) {
            return "<?php echo theme_asset({$expression}); ?>";
        });

        // @themeConfig directive for theme configuration
        Blade::directive('themeConfig', function ($expression) {
            return "<?php echo theme_config({$expression}); ?>";
        });

        // @darkMode directive for dark mode content
        Blade::directive('darkMode', function () {
            return "<?php if(theme_is_dark_mode()): ?>";
        });

        Blade::directive('enddarkMode', function () {
            return "<?php endif; ?>";
        });
    }

    /**
     * Register view composers for theme data
     */
    protected function registerViewComposers(): void
    {
        View::composer('*', function ($view) {
            $themeService = app(ThemeService::class);
            $view->with([
                'currentTheme' => $themeService->getCurrentTheme(),
                'themeConfig' => $themeService->getThemeConfig(),
                'isDarkMode' => $themeService->isDarkMode(),
                'availableThemes' => $themeService->getAvailableThemes(),
            ]);
        });
    }
}
