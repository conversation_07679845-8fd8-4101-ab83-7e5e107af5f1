<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Education extends Model
{
    use SoftDeletes;

    // uploads image folder name
    public $folderName = 'uploads/educations';

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        static::creating(function ($row) {
            if (auth()->check()) {
                $row->created_by = auth()->id();
            }
        });
        static::deleting(function ($row) {
            $row->deleted_by = auth()->id();
            $row->save();
            if ($row->isForceDeleting()) {
                // Here Write What you want make on delete
                unlinkOldFile($row->image, 'uploads/projects');

                //delete all project images
                $row->images->each->delete();
            }
        });
        parent::boot();
    }

}
