<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ResumeController extends Controller
{
    /**
     * Get all resume data
     */
    public function index(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'experience' => get_experience_data(),
                'education' => get_education_data(),
                'certifications' => get_certifications_data(),
                'skills' => get_skills_by_category(),
                'soft_skills' => get_soft_skills(),
                'resume_config' => get_resume_data(),
                'total_experience_years' => get_total_experience_years(),
            ]
        ]);
    }

    /**
     * Get work experience data
     */
    public function experience(): JsonResponse
    {
        $experiences = get_experience_data();
        
        // Add formatted duration to each experience
        foreach ($experiences as &$experience) {
            if (isset($experience['start_date'])) {
                $experience['formatted_duration'] = format_experience_duration(
                    $experience['start_date'],
                    $experience['end_date'] ?? null
                );
            }
        }

        return response()->json([
            'success' => true,
            'data' => $experiences
        ]);
    }

    /**
     * Get education data
     */
    public function education(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'education' => get_education_data(),
                'certifications' => get_certifications_data(),
            ]
        ]);
    }

    /**
     * Get skills data organized by categories
     */
    public function skills(): JsonResponse
    {
        $skillsData = get_skills_by_category();
        
        // Add icon paths to skills
        foreach ($skillsData as $categoryKey => &$category) {
            if (isset($category['skills'])) {
                foreach ($category['skills'] as &$skill) {
                    if (isset($skill['icon'])) {
                        $skill['icon_path'] = get_skill_icon_path($skill['icon']);
                    }
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'technical_skills' => $skillsData,
                'soft_skills' => get_soft_skills(),
            ]
        ]);
    }

    /**
     * Get specific experience by index
     */
    public function getExperience(int $index): JsonResponse
    {
        $experiences = get_experience_data();
        
        if (!isset($experiences[$index])) {
            return response()->json([
                'success' => false,
                'message' => 'Experience not found'
            ], 404);
        }

        $experience = $experiences[$index];
        
        // Add formatted duration
        if (isset($experience['start_date'])) {
            $experience['formatted_duration'] = format_experience_duration(
                $experience['start_date'],
                $experience['end_date'] ?? null
            );
        }

        return response()->json([
            'success' => true,
            'data' => $experience
        ]);
    }

    /**
     * Get specific education by index
     */
    public function getEducation(int $index): JsonResponse
    {
        $education = get_education_data();
        
        if (!isset($education[$index])) {
            return response()->json([
                'success' => false,
                'message' => 'Education record not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $education[$index]
        ]);
    }

    /**
     * Get skills by specific category
     */
    public function getSkillsByCategory(string $category): JsonResponse
    {
        $skillsData = get_skills_by_category();
        
        if (!isset($skillsData[$category])) {
            return response()->json([
                'success' => false,
                'message' => 'Skill category not found'
            ], 404);
        }

        $categoryData = $skillsData[$category];
        
        // Add icon paths to skills
        if (isset($categoryData['skills'])) {
            foreach ($categoryData['skills'] as &$skill) {
                if (isset($skill['icon'])) {
                    $skill['icon_path'] = get_skill_icon_path($skill['icon']);
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => $categoryData
        ]);
    }

    /**
     * Get resume summary data
     */
    public function summary(): JsonResponse
    {
        $resumeData = get_resume_data();
        
        return response()->json([
            'success' => true,
            'data' => [
                'summary' => $resumeData['sections']['summary'] ?? [],
                'contact_info' => $resumeData['contact_info'] ?? [],
                'total_experience_years' => get_total_experience_years(),
                'total_projects' => theme_config_get('about.projects_completed', 0),
                'happy_clients' => theme_config_get('about.happy_clients', 0),
            ]
        ]);
    }

    /**
     * Search through experience and education
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (empty($query)) {
            return response()->json([
                'success' => false,
                'message' => 'Search query is required'
            ], 400);
        }

        $results = [
            'experience' => [],
            'education' => [],
            'skills' => []
        ];

        // Search in experience
        foreach (get_experience_data() as $index => $experience) {
            if (stripos($experience['title'], $query) !== false ||
                stripos($experience['company'], $query) !== false ||
                stripos($experience['description'], $query) !== false) {
                $results['experience'][] = array_merge($experience, ['index' => $index]);
            }
        }

        // Search in education
        foreach (get_education_data() as $index => $education) {
            if (stripos($education['title'], $query) !== false ||
                stripos($education['institution'], $query) !== false ||
                stripos($education['field'], $query) !== false) {
                $results['education'][] = array_merge($education, ['index' => $index]);
            }
        }

        // Search in skills
        foreach (get_skills_by_category() as $categoryKey => $category) {
            if (isset($category['skills'])) {
                foreach ($category['skills'] as $skill) {
                    if (stripos($skill['name'], $query) !== false) {
                        $results['skills'][] = array_merge($skill, ['category' => $categoryKey]);
                    }
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => $results,
            'query' => $query
        ]);
    }
}
