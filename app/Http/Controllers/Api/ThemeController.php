<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ThemeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ThemeController extends Controller
{
    protected ThemeService $themeService;

    public function __construct(ThemeService $themeService)
    {
        $this->themeService = $themeService;
    }

    /**
     * Get all available themes
     */
    public function index(): JsonResponse
    {
        $themes = $this->themeService->getAvailableThemes();
        $currentTheme = $this->themeService->getCurrentThemeName();

        return response()->json([
            'success' => true,
            'data' => [
                'themes' => $themes,
                'current_theme' => $currentTheme,
                'dark_mode' => $this->themeService->isDarkMode(),
            ]
        ]);
    }

    /**
     * Get current theme information
     */
    public function current(): JsonResponse
    {
        $currentTheme = $this->themeService->getCurrentTheme();

        if (!$currentTheme) {
            return response()->json([
                'success' => false,
                'message' => 'No theme is currently active'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'theme' => $currentTheme,
                'config' => $this->themeService->getThemeConfig(),
                'dark_mode' => $this->themeService->isDarkMode(),
            ]
        ]);
    }

    /**
     * Switch to a different theme
     */
    public function switch(Request $request): JsonResponse
    {
        $request->validate([
            'theme' => [
                'required',
                'string',
                Rule::in(array_keys($this->themeService->getAvailableThemes()))
            ]
        ]);

        $success = $this->themeService->switchTheme($request->theme);

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to switch theme'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Theme switched successfully',
            'data' => [
                'current_theme' => $this->themeService->getCurrentThemeName(),
                'theme_data' => $this->themeService->getCurrentTheme(),
            ]
        ]);
    }

    /**
     * Toggle dark mode
     */
    public function toggleDarkMode(): JsonResponse
    {
        if (!$this->themeService->supports('dark-mode')) {
            return response()->json([
                'success' => false,
                'message' => 'Current theme does not support dark mode'
            ], 400);
        }

        $darkMode = $this->themeService->toggleDarkMode();

        return response()->json([
            'success' => true,
            'message' => 'Dark mode toggled successfully',
            'data' => [
                'dark_mode' => $darkMode
            ]
        ]);
    }

    /**
     * Get theme configuration
     */
    public function config(Request $request): JsonResponse
    {
        $key = $request->query('key');
        $config = $this->themeService->getThemeConfig($key);

        return response()->json([
            'success' => true,
            'data' => [
                'config' => $config
            ]
        ]);
    }

    /**
     * Get theme metadata
     */
    public function metadata(string $themeName): JsonResponse
    {
        $metadata = $this->themeService->getThemeMetadata($themeName);

        if (!$metadata) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'metadata' => $metadata
            ]
        ]);
    }

    /**
     * Check if theme supports a feature
     */
    public function supports(Request $request): JsonResponse
    {
        $request->validate([
            'feature' => 'required|string'
        ]);

        $supports = $this->themeService->supports($request->feature);

        return response()->json([
            'success' => true,
            'data' => [
                'feature' => $request->feature,
                'supported' => $supports
            ]
        ]);
    }

    /**
     * Clear theme cache
     */
    public function clearCache(): JsonResponse
    {
        $this->themeService->clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Theme cache cleared successfully'
        ]);
    }

    /**
     * Get theme assets information
     */
    public function assets(string $themeName): JsonResponse
    {
        if (!$this->themeService->exists($themeName)) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        // Get theme assets
        $assetsPath = public_path("themes/{$themeName}");
        $assets = [];

        if (is_dir($assetsPath)) {
            $files = glob($assetsPath . '/**/*', GLOB_BRACE);
            foreach ($files as $file) {
                if (is_file($file)) {
                    $relativePath = str_replace($assetsPath . '/', '', $file);
                    $assets[] = [
                        'path' => $relativePath,
                        'url' => asset("themes/{$themeName}/{$relativePath}"),
                        'size' => filesize($file),
                        'modified' => filemtime($file),
                    ];
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'theme' => $themeName,
                'assets' => $assets
            ]
        ]);
    }
}
