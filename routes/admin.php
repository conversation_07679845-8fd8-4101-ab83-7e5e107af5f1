<?php

use Illuminate\Support\Facades\Route;

Route::group(['as' => 'admin.'], function () {
//Route::group(['middleware' => ['web', 'auth', 'admin'], 'prefix' => 'admin'], function () {

    // dashboard
    Route::get('/', 'AdminController@index');

    // settings
    Route::resource('/settings', 'SettingController');
    Route::get('/list/settings', 'SettingController@list');
    Route::post('/setting/saveSettingChanges', 'Setting<PERSON>ontroller@saveSettingChanges');
    Route::get('/profile', 'SettingController@profile');
    Route::post('/change-password', 'Setting<PERSON>ontroller@saveChangePassword')->name('changePassword');


    // projects
    Route::resource('/projects', 'ProjectController');

    // skills
    Route::resource('/skills', 'SkillController');

    // works
    Route::resource('/works', 'WorkController');

    // works
    Route::resource('/educations', 'EducationController');

    // colors
    Route::resource('/hobbies', 'HobbyController');

    // colors
    Route::resource('/colors', 'ColorController');

    // socials
    Route::resource('/socials', 'SocialController');

//    // Images
//    Route::resource('/images', 'ImageController');


    // users
    Route::resource('/users', 'UserController');

    // roles
    Route::resource('/roles', 'RoleController');


    // menus
    Route::resource('/menus', 'MenuController');


    // menus
    Route::resource('/menus', 'MenuController');

    // menu item
    Route::get('/menu-item/orderData', 'MenuItemController@orderData');
    Route::resource('/menu-item', 'MenuItemController');

    // pages
    Route::resource('/pages', 'PageController');


    // posts
    Route::resource('/posts', 'PostController');

    // categories
    Route::resource('/categories', 'CategoryController');

    // tags
    Route::resource('/tags', 'TagController');

    // messages
    Route::resource('/messages', 'MessageController');

    // subscribers
    Route::resource('/subscribe', 'SubscribeController');


    // countries
    Route::resource('/countries', 'CityController');
    // cities
    Route::resource('/cities', 'CityController');
    // regions
    Route::resource('/regions', 'RegionController');
    // addresses
    Route::resource('/addresses', 'AddressController');

    // users
    Route::resource('/users', 'UserController');

    // roles
    Route::resource('/roles', 'RoleController');

    // settings
    Route::resource('/settings', 'SettingController');
    Route::get('/list/settings', 'SettingController@list');
    Route::post('/setting/saveSettingChanges', 'SettingController@saveSettingChanges');
    Route::get('/profile', 'SettingController@profile');
    Route::post('/change-password', 'SettingController@saveChangePassword')->name('changePassword');


    /*******************************************************
     * Global Functions
     *******************************************************/

    // locked page every 5 seconds
    Route::post('/{model}/refresh-locked', 'AdminController@refresh-model_locked')->name('model.refresh_locked');

    // delete multi row of model
    Route::post('/{model}/bulkDelete', 'AdminController@bulkDelete');

    // upload tinymce image for all models
    Route::post('/{folder}/upload-from-tiny', 'AdminController@upload_from_tiny');

});


